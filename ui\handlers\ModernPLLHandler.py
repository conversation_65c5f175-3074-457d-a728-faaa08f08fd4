﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的PLL处理器
使用ModernBaseHandler作为基类，重构自原PLLHandler
主要功能：PLL1和PLL2的配置管理、频率计算、时钟源管理
"""

from PyQt5 import QtCore
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.handlers.CrossRegisterWidgetManager import CrossRegisterWidgetManager, CrossRegisterWidgetConfig
from ui.forms.Ui_PLL1_2 import Ui_PLL1_2
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernPLLHandler(ModernBaseHandler):
    """现代化的PLL处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    # 定义PLL锁存状态信号
    pll1_lock_status_changed = QtCore.pyqtSignal(bool, float, float)  # (is_locked, r_divider_output, n_divider_output)
    pll2_lock_status_changed = QtCore.pyqtSignal(bool, float, float)  # (is_locked, r_divider_output, n_divider_output)

    # === 类常量配置 ===

    # 默认时钟源频率配置
    DEFAULT_CLOCK_FREQUENCIES = {
        "ClkIn0": 122.88,
        "ClkIn1": 245.76,
        "ClkIn2": 0,
        "ClkIn3": 0
    }

    # 默认时钟源分频配置
    DEFAULT_CLOCK_DIVIDERS = {
        "ClkIn0": 120,
        "ClkIn1": 120,
        "ClkIn2": 120,
        "ClkIn3": 120
    }

    # VCO模式频率范围配置 (单位: MHz)
    VCO_MODE_FREQUENCY_RANGES = {
        0: {"name": "VCO 0", "min": 2200.0, "max": 2800.0},  # VCO0: 2.2-2.8GHz
        1: {"name": "VCO 1", "min": 2500.0, "max": 3400.0},  # VCO1: 2.5-3.4GHz
        2: {"name": "CLKin1", "source": "clkin1"},           # CLKin1: 显示时钟选择页面的clkin1值
        3: {"name": "Fin0", "source": "fin0"}                # Fin0: 显示Fin0Freq控件的值
    }

    # ComboBox选项映射配置
    COMBOBOX_OPTIONS_MAP = {
        "comboPLL1WindSize": {0: "4ns", 1: "9ns", 2: "19ns", 3: "43ns"},
        "PLL2WINDSIZE": {0: "Reserved", 1: "1ns", 2: "18ns", 3: "26ns"},
        "PLL1CPState": {0: "Active", 1: "Tristate"},
        "PLL2CPState": {0: "Active", 1: "Tristate"},
        "PLL1PFDPolarity": {0: "Negative", 1: "Positive"},
        "PLL2PFDPolarity": {0: "Negative", 1: "Positive"},
        "PLL1CPGain": {i: f"{50 + i*100}μA" for i in range(16)},
        "PLL2CPGain": {0: "Reserved", 1: "Reserved", 2: "1600uA", 3: "3200uA"},
        "PLL2R3": {0: "2.4KOhm", 1: "0.2KOhm", 2: "0.5KOhm", 4: "1.1KOhm"},
        "PLL2C1": {0: "10pF", 1: "20pF", 2: "40pF"},
        "PLL2C3": {0: "10pF", 1: "20pF", 2: "40pF"},
        "PLL1NclkMux": {0: "OSCin", 1: "Feedback Mux", 2: "PLL2 Prescaler"},
        "PLL2NclkMux": {0: "PLL2 Prescaler", 1: "Feedback Mux"},
        "PLL2RclkMux": {0: "OSCin", 1: "PLL1 CLKinX"},
        "PLL2Prescaler": {i: str(i) if i == 0 else str(i) for i in range(8)},
        "FBMUX": {0: "CLKout6", 1: "CLKout8", 2: "SYSREF Divider", 4: "External"},
        "comboVcoMode": {0: "VCO 0", 1: "VCO 1", 2: "CLKin1", 3: "Fin0"},
        "Doubler": {0: "1×", 1: "2×"},
        "Fin0InputType": {0: "Diff Input", 1: "Single Ended Input(Fin)",
                         2: "Single Ended Input(Fin*)", 3: "Reserved"},
        "OSCin_FREQ": {0: "0-63MHz,not valid", 1: "div1 -> 63-127MHz",
                      2: "div2 -> 127-255MHz", 3: "div3 -> Reserved",
                      4: "div4 -> 255-500MHz", 5: "div5 -> Reserved",
                      6: "div6 -> Reserved", 7: "div7 -> Reserved"},
        "DACClkMult": {0: "4", 1: "64", 2: "1024", 3: "16384"},
        "RGHOExitDacassistStep": {0: "slowest", 1: "slow", 2: "fast", 3: "fastest"},
        "FCALM1": {i: str(i) for i in range(256)},
        "FCALM2": {i: str(i) for i in range(16384)}
    }

    # 特殊ComboBox映射（PLL2R3的值映射）
    PLL2R3_VALUE_MAP = [0, 1, 2, 4]  # 实际寄存器值

    # 已知控件的默认值映射
    KNOWN_WIDGET_DEFAULTS = {
        "comboPLL1WindSize": 3,      # 寄存器0x6B位7:6，默认值"11"=3
        "PLL2WINDSIZE": 2,           # 寄存器0x79位6:5，默认值"10"=2
        "PLL1CPState": 0,            # 寄存器0x6B位5，默认值"0"=0
        "PLL2CPState": 0,            # 假设默认值为0
        "PLL1PFDPolarity": 1,        # 寄存器0x6B位4，默认值"1"=1
        "PLL2PFDPolarity": 0,        # 假设默认值为0
        "PLL1CPGain": 0,             # 假设默认值为0
        "PLL2CPGain": 2,             # 假设默认值为2
        "PLL1NclkMux": 0,            # 假设默认值为0
        "PLL2NclkMux": 0,            # 假设默认值为0
        "PLL2RclkMux": 0,            # 假设默认值为0
        "PLL2Prescaler": 0,          # 假设默认值为0
        "FBMUX": 0,                  # 假设默认值为0
        "comboVcoMode": 0,           # 假设默认值为0
        "Doubler": 0,                # 假设默认值为0
        "Fin0InputType": 0,          # 假设默认值为0
        "OSCinFreq": 0,              # 假设默认值为0
        "DACClkMult": 0,             # 假设默认值为0
        "RGHOExitDacassistStep": 0,  # 假设默认值为0
    }

    def __init__(self, parent=None, register_manager=None, main_window=None, **kwargs):
        """初始化现代化PLL处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            main_window: 主窗口引用（用于避免控件变化时的引用问题）
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 立即设置主窗口引用（在任何可能触发控件变化的操作之前）
        if main_window:
            self.main_window = main_window
            logger.info(f"ModernPLLHandler: 已在构造函数中设置主窗口引用: {type(main_window)}")

        # 设置窗口标题
        self.setWindowTitle("PLL配置 (现代化版本)")

        # 初始化VCO模式设置标志
        self._vco_mode_setting = False

        # 创建UI实例
        self.ui = Ui_PLL1_2()
        self.ui.setupUi(self.content_widget)

        # 窗口名称，用于缓存标识（必须在其他初始化之前设置）
        self._window_name = "PLLControl"

        # 初始化模式缓存
        self._current_mode_cache = None

        # 初始化警告防重复机制
        self._last_clk_output_warning_time = 0
        self._last_clk_output_connection_warning_time = 0
        self._last_vco_dist_freq_error_time = 0
        self._clk_output_warning_interval = 5.0  # 5秒内不重复警告

        # 初始化PLL特定配置
        self._init_pll_config()

        # 在所有初始化完成后，进行初始频率计算
        QtCore.QTimer.singleShot(100, self._perform_initial_calculations)

        # 确保内容完全可见
        QtCore.QTimer.singleShot(200, self.ensure_content_fully_visible)

        logger.info("现代化PLL处理器初始化完成")



    def _init_pll_config(self):
        """初始化PLL特定配置"""
        try:
            # 防止循环调用的标志
            self._updating_pll2_n_divider = False

            # 初始化跨寄存器控件管理器
            self.cross_register_manager = CrossRegisterWidgetManager(self.register_manager)
            self.cross_register_manager.set_ui(self.ui)

            # 初始化时钟源配置（参考传统PLL处理器）
            self._init_clock_source_config()

            # 如果没有从RegisterUpdateBus获取到当前时钟源，则使用默认值
            if not hasattr(self, 'current_clock_source') or not self.current_clock_source:
                self.current_clock_source = "ClkIn0"
                logger.info("使用默认时钟源: ClkIn0")

            # 初始化UI默认值
            self._init_ui_defaults()

            # 设置控件范围
            self._setup_widget_ranges()

            # 注册跨寄存器控件（参考传统PLL处理器）
            self._register_cross_register_controls()

            # 连接特殊信号
            self._connect_special_signals()

            logger.info("PLL特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化PLL配置时出错: {str(e)}")

    def _post_initialization_setup(self):
        """后初始化设置（在控件映射构建完成后执行）"""
        try:
            # 初始化PLL2Cin控件（需要在widget_register_map构建完成后执行）
            self._init_pll2cin_control()

            # 缓存当前PLL2PFD频率值，供同步系统参考窗口使用
            self._cache_initial_pll2_pfd_frequency()

            logger.info("PLL后初始化设置完成")
        except Exception as e:
            logger.error(f"PLL后初始化设置时出错: {str(e)}")

    def _init_clock_source_config(self):
        """初始化时钟源配置（参考传统PLL处理器）"""
        try:
            # 使用类常量初始化默认配置
            self.clkin_frequencies = self.DEFAULT_CLOCK_FREQUENCIES.copy()
            self.clkin_divider_values = self.DEFAULT_CLOCK_DIVIDERS.copy()

            # 尝试从RegisterUpdateBus获取时钟源配置
            try:
                clock_bus = RegisterUpdateBus.instance()
                self._load_clock_config_from_bus(clock_bus)
                logger.info(f"从RegisterUpdateBus获取时钟源配置: {self.current_clock_source}")

            except Exception as e:
                logger.warning(f"无法从RegisterUpdateBus获取时钟源配置，使用默认值: {str(e)}")

            logger.info(f"时钟源配置初始化完成: 频率={self.clkin_frequencies}, 分频={self.clkin_divider_values}")

        except Exception as e:
            logger.error(f"初始化时钟源配置时出错: {str(e)}")
            # 使用最基本的默认配置
            self.clkin_frequencies = self.DEFAULT_CLOCK_FREQUENCIES.copy()
            self.clkin_divider_values = self.DEFAULT_CLOCK_DIVIDERS.copy()

    def _load_clock_config_from_bus(self, clock_bus):
        """从RegisterUpdateBus加载时钟源配置"""
        # 确保RegisterUpdateBus已从寄存器初始化
        if not clock_bus.is_initialized_from_registers():
            logger.warning("RegisterUpdateBus尚未从寄存器初始化，使用默认配置")

        # 获取RegisterUpdateBus中存储的时钟源配置
        for source in ["ClkIn0", "ClkIn1", "ClkIn2", "ClkIn3"]:
            freq = clock_bus.get_clock_frequency(source)
            divider = clock_bus.get_clock_divider(source)

            # 只有在有配置的情况下才更新
            if freq > 0:
                self.clkin_frequencies[source] = freq
                logger.debug(f"从RegisterUpdateBus加载 {source} 频率: {freq}MHz")
            if divider > 0:
                self.clkin_divider_values[source] = divider
                logger.debug(f"从RegisterUpdateBus加载 {source} 分频值: {divider}")

        # 获取当前时钟源
        current_source = clock_bus.get_current_clock_source()
        if current_source:
            self.current_clock_source = self._normalize_clock_source_name(current_source)
            logger.info(f"从RegisterUpdateBus获取当前时钟源: {current_source} -> {self.current_clock_source}")
        else:
            logger.warning("RegisterUpdateBus中没有当前时钟源信息，使用默认值")

    def _normalize_clock_source_name(self, source_name):
        """规范化时钟源名称"""
        if source_name.startswith("CLK"):
            return "ClkIn" + source_name[5:]
        return source_name

    # === 通用工具方法 ===

    def _get_register_value_safely(self, widget_name, reg_addr, bit_def_name):
        """安全地从RegisterManager获取寄存器值

        Args:
            widget_name: 控件名称
            reg_addr: 寄存器地址
            bit_def_name: 位字段名称

        Returns:
            tuple: (value, success) - 值和是否成功获取
        """
        if not self.register_manager or not reg_addr or not bit_def_name:
            return None, False

        try:
            value = self.register_manager.get_bit_field_value(reg_addr, bit_def_name)
            if value is not None:
                logger.info(f"从RegisterManager获取 {widget_name} 值: {value} (reg={reg_addr}, bit='{bit_def_name}')")
                return value, True
            else:
                logger.info(f"RegisterManager中未找到 {widget_name} ({reg_addr}[{bit_def_name}]) 的值")
                return None, False
        except Exception as e:
            logger.warning(f"从RegisterManager获取 {widget_name} ({reg_addr}[{bit_def_name}]) 值失败: {e}")
            return None, False

    def _clamp_value_to_range(self, value, min_val, max_val, widget_name=""):
        """将值限制在指定范围内

        Args:
            value: 要检查的值
            min_val: 最小值
            max_val: 最大值
            widget_name: 控件名称（用于日志）

        Returns:
            tuple: (clamped_value, was_clamped) - 限制后的值和是否被限制
        """
        if min_val <= value <= max_val:
            return value, False

        clamped_value = max(min_val, min(max_val, value))
        logger.warning(f"{widget_name} 值 {value} 超出范围 [{min_val}-{max_val}]，修正为 {clamped_value}")
        return clamped_value, True

    def _parse_default_value(self, default_value_str):
        """解析默认值字符串

        Args:
            default_value_str: 默认值字符串

        Returns:
            tuple: (parsed_value, success) - 解析后的值和是否成功
        """
        if not default_value_str:
            return 0, False

        default_value_str = default_value_str.strip()

        # 尝试解析二进制
        if all(c in '01' for c in default_value_str):
            try:
                return int(default_value_str, 2), True
            except ValueError:
                pass

        # 尝试解析十进制
        if default_value_str.isdigit():
            try:
                return int(default_value_str), True
            except ValueError:
                pass

        return 0, False

    def _init_ui_defaults(self):
        """初始化UI默认值"""
        try:
            # 使用类常量配置ComboBox选项映射
            self.combobox_options_map = self.COMBOBOX_OPTIONS_MAP.copy()

            # 初始化ComboBox控件
            self._init_combobox_controls()

            # 设置默认频率值
            self._set_default_frequency_values()

            logger.info("UI默认值初始化完成")

        except Exception as e:
            logger.error(f"初始化UI默认值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _set_default_frequency_values(self):
        """设置默认频率值"""
        frequency_widgets = {
            "OSCinFreq": "122.88",
            "ExternalVCXOFreq": "122.88",
            "FreFin": str(self.clkin_frequencies[self.current_clock_source]),
            "VCODistFreq": "0.00",  # VCODistFreq初始值，将在频率计算中更新
            "Fin0Freq": "2949.12"   # Fin0Freq默认值设置为2949.12
        }

        # 首先从缓存恢复值
        self._restore_lineedit_values_from_cache()

        for widget_name, default_value in frequency_widgets.items():
            if hasattr(self.ui, widget_name):
                widget = getattr(self.ui, widget_name)

                # 如果缓存中没有值，则使用默认值
                if not widget.text():
                    widget.setText(default_value)

                # 设置计算结果控件为只读
                if widget_name in ["VCODistFreq", "PLL1PFDFreq", "PLL2PFDFreq"]:
                    widget.setReadOnly(True)
                    logger.info(f"设置 {widget_name} 为只读（计算结果显示）")
                elif widget_name == "Fin0Freq":
                    # Fin0Freq的只读状态取决于VCO模式，这里先设为可编辑，后续根据模式调整
                    widget.setReadOnly(False)
                    logger.info(f"设置 {widget_name} 为可编辑（输入控件）")

        # 确保OSCinFreq和ExternalVCXOFreq的值保持一致
        self._ensure_frequency_widgets_sync()

    def _init_pll2cin_control(self):
        """初始化PLL2Cin控件"""
        try:
            if hasattr(self.ui, "PLL2Cin"):
                # 设置为只读
                self.ui.PLL2Cin.setReadOnly(True)
                # 设置默认值
                self.ui.PLL2Cin.setText("0.00000")

                # 从RegisterManager获取FBMuxEn的实际状态
                fb_mux_enabled = self._get_fbmuxen_register_value()
                logger.info(f"从寄存器获取FBMuxEn状态: {fb_mux_enabled}")

                # 根据寄存器值设置FBMuxEn控件状态
                if hasattr(self.ui, "FBMuxEn"):
                    self.ui.FBMuxEn.setChecked(fb_mux_enabled)
                    logger.info(f"设置FBMuxEn控件状态: {fb_mux_enabled}")

                # 根据实际状态设置PLL2Cin可见性
                self.ui.PLL2Cin.setVisible(fb_mux_enabled)
                if fb_mux_enabled:
                    # 强制显示控件
                    self.ui.PLL2Cin.show()
                    self.ui.PLL2Cin.setHidden(False)
                    # 确保控件可用
                    self.ui.PLL2Cin.setEnabled(True)
                    # 初始化PLL2Cin的值（从缓存或窗口获取）
                    self._init_pll2cin_value()
                else:
                    # 强制隐藏控件
                    self.ui.PLL2Cin.hide()
                    self.ui.PLL2Cin.setHidden(True)

                logger.info(f"设置PLL2Cin可见性: {fb_mux_enabled}")

                # 验证设置结果
                actual_checked = self.ui.FBMuxEn.isChecked() if hasattr(self.ui, "FBMuxEn") else False
                actual_visible = self.ui.PLL2Cin.isVisible()
                actual_hidden = self.ui.PLL2Cin.isHidden()
                actual_enabled = self.ui.PLL2Cin.isEnabled()
                logger.info(f"验证结果 - FBMuxEn实际状态: {actual_checked}")
                logger.info(f"PLL2Cin - 可见性: {actual_visible}, 隐藏: {actual_hidden}, 启用: {actual_enabled}")

                # 如果可见，更新数值
                if fb_mux_enabled:
                    self._update_pll2cin_value()

                logger.info("PLL2Cin控件初始化完成")
            else:
                logger.warning("PLL2Cin控件不存在")
        except Exception as e:
            logger.error(f"初始化PLL2Cin控件时出错: {str(e)}")

    def _init_pll2cin_value(self):
        """初始化PLL2Cin控件的值

        调用常规更新方法获取当前值
        """
        try:
            if not hasattr(self.ui, "PLL2Cin"):
                return

            # 确保PLL2PFD已计算（PLL2Cin依赖PLL2PFD值）
            self._ensure_pll2_pfd_calculated()

            # 确保PLL2PFD已计算（PLL2Cin依赖PLL2PFD值）
            self._ensure_pll2_pfd_calculated()

            # 触发系统参考界面重新计算InternalVCO（如果界面已打开）
            self._trigger_sysref_window_recalculation()

            # 调用常规更新方法
            self._update_pll2cin_value()

        except Exception as e:
            logger.error(f"初始化PLL2Cin值时出错: {str(e)}")

    def force_refresh_pll2cin(self):
        """强制刷新PLL2Cin值 - 用于调试"""
        try:
            logger.info("【强制刷新】开始强制刷新PLL2Cin...")

            # 检查FB_MUX_EN状态
            if hasattr(self.ui, "FBMuxEn"):
                fb_mux_enabled = self.ui.FBMuxEn.isChecked()
                logger.info(f"【强制刷新】FB_MUX_EN状态: {fb_mux_enabled}")

                if fb_mux_enabled:
                    # 强制更新PLL2Cin可见性和值
                    self._update_pll2cin_visibility()
                    self._update_pll2cin_value()
                    logger.info("【强制刷新】已强制更新PLL2Cin")
                else:
                    logger.warning("【强制刷新】FB_MUX_EN未勾选，无法更新PLL2Cin")
            else:
                logger.error("【强制刷新】FBMuxEn控件不存在")

        except Exception as e:
            logger.error(f"强制刷新PLL2Cin时出错: {str(e)}")

    def _get_fbmuxen_register_value(self):
        """从RegisterManager获取FBMuxEn的寄存器值"""
        try:
            if self.register_manager:
                # 方法1：通过控件映射获取寄存器信息
                if hasattr(self, 'widget_register_map') and 'FBMuxEn' in self.widget_register_map:
                    widget_info = self.widget_register_map['FBMuxEn']
                    register_addr = widget_info.get('register_addr')
                    bit_def = widget_info.get('bit_def', {})
                    bit_field_name = bit_def.get('name')  # 应该是 "FB_MUX_EN"

                    if register_addr and bit_field_name:
                        try:
                            bit_value = self.register_manager.get_bit_field_value(register_addr, bit_field_name)
                            # 安全的地址格式化
                            addr_str = f"0x{register_addr:02X}" if isinstance(register_addr, int) else str(register_addr)
                            logger.debug(f"从寄存器{addr_str}[{bit_field_name}]获取FBMuxEn值: {bit_value}")
                            return bool(bit_value)
                        except Exception as e:
                            logger.warning(f"从寄存器获取FBMuxEn值失败: {str(e)}")

                # 方法2：搜索FB_MUX_EN位字段
                try:
                    search_results = self.register_manager.search_bit_fields("FB_MUX_EN")
                    if search_results:
                        bit_field_name, register_addr, register_name = search_results[0]
                        bit_value = self.register_manager.get_bit_field_value(register_addr, bit_field_name)
                        # 安全的地址格式化
                        addr_str = f"0x{register_addr:02X}" if isinstance(register_addr, int) else str(register_addr)
                        logger.debug(f"通过搜索从寄存器{addr_str}[{bit_field_name}]获取FBMuxEn值: {bit_value}")
                        return bool(bit_value)
                except Exception as e:
                    logger.warning(f"搜索FB_MUX_EN位字段失败: {str(e)}")

                logger.warning("无法从RegisterManager获取FBMuxEn值")
            else:
                logger.warning("RegisterManager不存在，无法获取FBMuxEn寄存器值")

            # 如果无法从寄存器获取，检查控件当前状态
            if hasattr(self.ui, "FBMuxEn"):
                current_state = self.ui.FBMuxEn.isChecked()
                logger.debug(f"使用FBMuxEn控件当前状态: {current_state}")
                return current_state

            # 默认返回False（未选中）
            logger.debug("FBMuxEn默认返回False")
            return False

        except Exception as e:
            logger.error(f"获取FBMuxEn寄存器值时出错: {str(e)}")
            # 出错时检查控件状态作为备选
            if hasattr(self.ui, "FBMuxEn"):
                return self.ui.FBMuxEn.isChecked()
            return False

    def _ensure_frequency_widgets_sync(self):

        # 确保OSCinFreq和ExternalVCXOFreq的值保持一致
        self._ensure_frequency_widgets_sync()

    def _ensure_frequency_widgets_sync(self):
        """确保OSCinFreq和ExternalVCXOFreq控件的值保持一致"""
        try:
            # 检查两个控件是否都存在
            if not (hasattr(self.ui, "OSCinFreq") and hasattr(self.ui, "ExternalVCXOFreq")):
                return

            oscin_value = self.ui.OSCinFreq.text()
            external_vcxo_value = self.ui.ExternalVCXOFreq.text()

            # 如果值不一致，以OSCinFreq的值为准（因为它在频率计算中被使用）
            if oscin_value != external_vcxo_value:
                self.ui.ExternalVCXOFreq.setText(oscin_value)
                logger.info(f"初始化时同步频率控件: OSCinFreq -> ExternalVCXOFreq, 值: {oscin_value}")

        except Exception as e:
            logger.error(f"确保频率控件同步时出错: {str(e)}")

    def _init_combobox_controls(self):
        """初始化ComboBox控件"""
        try:
            from PyQt5.QtWidgets import QComboBox

            # 为所有ComboBox控件设置选项
            for widget_name, options in self.combobox_options_map.items():
                if hasattr(self.ui, widget_name):
                    combo_box = getattr(self.ui, widget_name)
                    if isinstance(combo_box, QComboBox):
                        self._setup_combobox_options(combo_box, widget_name, options)
                        self._set_combobox_default_value(combo_box, widget_name)

            logger.info("ComboBox控件初始化完成")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化ComboBox控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _setup_combobox_options(self, combo_box, widget_name, options):
        """设置ComboBox选项"""
        combo_box.clear()

        # 特殊处理PLL2R3控件
        if widget_name == "PLL2R3":
            texts = ["2.4KOhm", "0.2KOhm", "0.5KOhm", "1.1KOhm"]
            for value, text in zip(self.PLL2R3_VALUE_MAP, texts):
                combo_box.addItem(text, value)
            logger.info(f"已为 {widget_name} 设置特殊映射: {self.PLL2R3_VALUE_MAP} -> {texts}")
        else:
            # 常规ComboBox处理
            for value, text in sorted(options.items()):
                combo_box.addItem(text, value)
            logger.info(f"已为 {widget_name} 设置选项映射")

    def _set_combobox_default_value(self, combo_box, widget_name):
        """设置ComboBox的默认值，优先从RegisterManager获取"""
        try:
            default_index = 0
            widget_info = self.widget_register_map.get(widget_name)

            # 1. 尝试从RegisterManager获取值
            reg_val, success = self._try_get_register_value(widget_info, widget_name)
            if success:
                default_index = self._find_combobox_index_by_value(combo_box, reg_val, widget_name)
                if default_index != -1:
                    logger.info(f"为 {widget_name} 从RegisterManager设置索引: {default_index}")
                else:
                    default_index = self._get_fallback_default_index(widget_name, widget_info)
            else:
                default_index = self._get_fallback_default_index(widget_name, widget_info)

            # 设置ComboBox值
            self._apply_combobox_index(combo_box, default_index, widget_name)

        except Exception as e:
            logger.error(f"设置 {widget_name} 默认值时出错: {str(e)}")
            self._apply_combobox_index(combo_box, 0, widget_name)

    def _try_get_register_value(self, widget_info, widget_name):
        """尝试从RegisterManager获取寄存器值"""
        if not widget_info or not self.register_manager:
            return None, False

        reg_addr = widget_info.get("register_addr")
        bit_def_name = widget_info.get("bit_def_name")

        if reg_addr and bit_def_name:
            return self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
        return None, False

    def _find_combobox_index_by_value(self, combo_box, value, widget_name):
        """在ComboBox中查找指定值对应的索引"""
        for i in range(combo_box.count()):
            if combo_box.itemData(i) == value:
                return i
        logger.warning(f"ComboBox {widget_name} 中未找到值 {value} 对应的项")
        return -1

    def _get_fallback_default_index(self, widget_name, widget_info):
        """获取备用默认索引"""
        # 特殊处理PLL2C1和PLL2C3控件
        if widget_name in ["PLL2C1", "PLL2C3"]:
            logger.info(f"为 {widget_name} 设置特殊默认值: 索引2 (40pF)")
            return 2

        # 尝试从widget_info解析默认值
        if widget_info and widget_info.get("default_value"):
            _, success = self._parse_default_value(widget_info["default_value"])
            if success and widget_name in self.KNOWN_WIDGET_DEFAULTS:
                return self.KNOWN_WIDGET_DEFAULTS[widget_name]

        # 使用已知默认值
        return self.KNOWN_WIDGET_DEFAULTS.get(widget_name, 0)

    def _apply_combobox_index(self, combo_box, index, widget_name):
        """应用ComboBox索引"""
        combo_box.blockSignals(True)
        try:
            if 0 <= index < combo_box.count():
                combo_box.setCurrentIndex(index)
            elif combo_box.count() > 0:
                logger.warning(f"{widget_name} 索引 {index} 超出范围，设为0")
                combo_box.setCurrentIndex(0)
            else:
                logger.warning(f"{widget_name} 为空，无法设置索引")

            # 更新内部跟踪的值
            if hasattr(self, 'widget_values') and combo_box.count() > 0:
                current_data = combo_box.itemData(combo_box.currentIndex())
                self.widget_values[widget_name] = current_data
                logger.debug(f"{widget_name} 初始值: {current_data} (索引: {combo_box.currentIndex()})")
        finally:
            combo_box.blockSignals(False)

    def _setup_widget_ranges(self):
        """设置控件范围"""
        try:
            # 设置PLL1NDivider控件范围 - 修复最大值为2^14-1=16383
            if hasattr(self.ui, "PLL1NDivider"):
                self.ui.PLL1NDivider.setMinimum(2)
                self.ui.PLL1NDivider.setMaximum(16383)  # 最大值为2^14-1
                logger.info("已设置PLL1NDivider控件范围: 2-16383")

            # 设置PLL2NDivider控件范围
            if hasattr(self.ui, "PLL2NDivider"):
                self.ui.PLL2NDivider.setMinimum(1)
                self.ui.PLL2NDivider.setMaximum(262143)  # 最大值为2^18-1

                # 确保控件可编辑
                self.ui.PLL2NDivider.setEnabled(True)
                if hasattr(self.ui.PLL2NDivider, 'setReadOnly'):
                    self.ui.PLL2NDivider.setReadOnly(False)

                # 强制刷新控件外观
                self.ui.PLL2NDivider.update()

                logger.info("已设置PLL2NDivider控件范围: 1-262143，并确保可编辑")
                logger.info(f"PLL2NDivider当前值: {self.ui.PLL2NDivider.value()}")
                logger.info(f"PLL2NDivider是否启用: {self.ui.PLL2NDivider.isEnabled()}")
                logger.info(f"PLL2NDivider是否只读: {self.ui.PLL2NDivider.isReadOnly()}")

                # 检查当前的PLL2NclkMux模式
                pll2_nclk_mux = self._get_pll2_nclk_mux_value()
                logger.info(f"当前PLL2NclkMux模式: {pll2_nclk_mux} ({'PLL2 Prescaler' if pll2_nclk_mux == 0 else 'Feedback Mux' if pll2_nclk_mux == 1 else '未知'})")

                if pll2_nclk_mux == 0:
                    logger.info("📋 VCO Dist Freq计算公式: PLL2PFDFreq × PLL2NDivider × PLL2Prescaler")
                elif pll2_nclk_mux == 1:
                    fb_mux_value = 0
                    if hasattr(self.ui, "FBMUX"):
                        if hasattr(self.ui.FBMUX, 'currentData'):
                            fb_mux_value = self.ui.FBMUX.currentData() or 0
                        else:
                            fb_mux_value = self.ui.FBMUX.currentIndex()

                    fb_source = "CLKout6" if fb_mux_value == 0 else "CLKout8" if fb_mux_value == 1 else f"其他({fb_mux_value})"
                    logger.info(f"📋 VCO Dist Freq计算公式: PLL2PFDFreq × PLL2NDivider × {fb_source}分频比")
                    logger.info(f"📋 当前FB_MUX选择: {fb_mux_value} ({fb_source})")

            # 设置PLL1RDividerSetting的最大值
            if hasattr(self.ui, "PLL1RDividerSetting"):
                self.ui.PLL1RDividerSetting.setMaximum(16383)
                logger.info("已设置PLL1RDividerSetting控件最大值: 16383")

            if hasattr(self.ui, "spinBoxPLL1DLDCNT"):
                self.ui.spinBoxPLL1DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL1DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL1DLDCNT控件范围: 0-16383")

            if hasattr(self.ui, "spinBoxPLL2DLDCNT"):
                self.ui.spinBoxPLL2DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL2DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL2DLDCNT控件范围: 0-16383")
            
            if hasattr(self.ui, "FCALM1"):
                self.ui.FCALM1.setMinimum(0)
                self.ui.FCALM1.setMaximum(255)
                logger.info("已设置FCALM1控件范围: 0-255")
            
            if hasattr(self.ui, "FCALM2"):
                self.ui.FCALM2.setMinimum(0)
                self.ui.FCALM2.setMaximum(16383)
                logger.info("已设置FCALM2控件范围: 0-16383")

        except Exception as e:
            logger.error(f"设置控件范围时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接OSCinFreq的信号到频率计算和同步功能
            if hasattr(self.ui, "OSCinFreq"):
                # 连接returnPressed信号（用户按回车时）
                self.ui.OSCinFreq.returnPressed.connect(self.calculate_output_frequencies)
                self.ui.OSCinFreq.returnPressed.connect(lambda: self._sync_frequency_widgets("OSCinFreq"))
                # 连接textChanged信号（用户输入时实时响应）
                self.ui.OSCinFreq.textChanged.connect(self.calculate_output_frequencies)
                self.ui.OSCinFreq.textChanged.connect(lambda: self._sync_frequency_widgets("OSCinFreq"))
                logger.info("已连接 OSCinFreq returnPressed 和 textChanged 信号")

            # 连接ExternalVCXOFreq的信号到频率计算和同步功能
            if hasattr(self.ui, "ExternalVCXOFreq"):
                # 连接returnPressed信号（用户按回车时）
                self.ui.ExternalVCXOFreq.returnPressed.connect(self.calculate_output_frequencies)
                self.ui.ExternalVCXOFreq.returnPressed.connect(lambda: self._sync_frequency_widgets("ExternalVCXOFreq"))
                # 连接textChanged信号（用户输入时实时响应）
                self.ui.ExternalVCXOFreq.textChanged.connect(self.calculate_output_frequencies)
                self.ui.ExternalVCXOFreq.textChanged.connect(lambda: self._sync_frequency_widgets("ExternalVCXOFreq"))
                logger.info("已连接 ExternalVCXOFreq returnPressed 和 textChanged 信号")

            # 连接FreFin的信号到频率计算功能（用于PLL2RclkMux=1时的计算）
            if hasattr(self.ui, "FreFin"):
                # 连接returnPressed信号（用户按回车时）
                self.ui.FreFin.returnPressed.connect(self.calculate_output_frequencies)
                # 连接textChanged信号（用户输入时实时响应）
                self.ui.FreFin.textChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 FreFin returnPressed 和 textChanged 信号")

            # 连接时钟源选择信号
            RegisterUpdateBus.instance().clock_source_selected.connect(self.on_global_clock_source_selected)
            logger.info("已连接时钟源选择信号")

            # 连接模式变化信号
            RegisterUpdateBus.instance().mode_changed.connect(self.on_mode_changed)
            logger.info("已连接模式变化信号")

            # 连接ModernSetModesHandler的模式变化信号
            self._connect_to_modes_handler()

            # 连接全局寄存器更新信号
            RegisterUpdateBus.instance().register_updated.connect(self.on_global_register_update)
            logger.info("已连接全局寄存器更新信号")

            # 连接InternalVCOFreq更新信号
            bus = RegisterUpdateBus.instance()
            if hasattr(bus, 'internal_vco_freq_updated'):
                bus.internal_vco_freq_updated.connect(self.on_internal_vco_freq_updated)
                logger.info("已连接InternalVCOFreq更新信号")
            else:
                logger.warning("RegisterUpdateBus中缺少internal_vco_freq_updated信号")

            # 连接SYSREF频率更新信号，用于PLL2Cin同步
            if hasattr(bus, 'sysref_freq_updated'):
                bus.sysref_freq_updated.connect(self._on_sysref_freq_updated)
                logger.info("已连接SYSREF频率更新信号")
            else:
                logger.warning("RegisterUpdateBus中缺少sysref_freq_updated信号")

            # 连接跨寄存器控件的信号
            self._connect_cross_register_widget_signals()

            # 连接MUX控件的信号，实现实时计算
            self._connect_mux_widget_signals()

            # 连接时钟输出窗口的分频器信号
            self._connect_clk_output_divider_signals()

            # 连接VCO模式控件信号
            self._connect_vco_mode_signals()

            # 连接Fin0相关控件信号
            self._connect_fin0_signals()

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _connect_to_modes_handler(self):
        """连接到ModernSetModesHandler的信号"""
        try:
            # 尝试从主窗口获取ModernSetModesHandler实例
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'get_tool_window'):
                modes_handler = main_window.get_tool_window('ModernSetModesHandler')
                if modes_handler:
                    # 连接模式变化信号
                    modes_handler.mode_changed.connect(self.on_mode_changed_from_handler)
                    logger.info("已连接到ModernSetModesHandler的模式变化信号")
                else:
                    logger.debug("ModernSetModesHandler实例未找到，将在需要时动态连接")
            else:
                logger.debug("主窗口未找到或不支持get_tool_window方法")

        except Exception as e:
            logger.error(f"连接到ModernSetModesHandler时出错: {str(e)}")

    def on_mode_changed_from_handler(self, mode_name):
        """处理来自ModernSetModesHandler的模式变化信号"""
        logger.info(f"ModernPLLHandler 收到来自ModernSetModesHandler的模式变化信号: {mode_name}")

        # 缓存模式名称
        self._current_mode_cache = mode_name

        # 延迟一点时间再计算，确保所有寄存器都已更新
        QtCore.QTimer.singleShot(100, self.calculate_output_frequencies)

    def _get_main_window(self):
        """获取主窗口实例"""
        try:
            if hasattr(self, 'main_window') and self.main_window:
                return self.main_window
            elif hasattr(self, 'parent') and self.parent:
                # 尝试从父级获取主窗口
                parent = self.parent
                while parent:
                    if hasattr(parent, 'get_tool_window'):
                        return parent
                    parent = getattr(parent, 'parent', None)
            return None
        except Exception as e:
            logger.error(f"获取主窗口时出错: {str(e)}")
            return None

    def _connect_cross_register_widget_signals(self):
        """连接跨寄存器控件的信号"""
        try:
            for widget_name in self.cross_register_manager.widget_configs.keys():
                if hasattr(self.ui, widget_name):
                    widget = getattr(self.ui, widget_name)

                    # 断开现有连接
                    try:
                        if hasattr(widget, 'valueChanged'):
                            widget.valueChanged.disconnect()
                        elif hasattr(widget, 'currentIndexChanged'):
                            widget.currentIndexChanged.disconnect()
                    except Exception:
                        pass

                    # 连接新信号
                    if hasattr(widget, 'valueChanged'):
                        widget.valueChanged.connect(
                            lambda value, w=widget_name: self.update_register_value(w, value)
                        )
                    elif hasattr(widget, 'currentIndexChanged'):
                        widget.currentIndexChanged.connect(
                            lambda index, w=widget_name: self.update_register_value(w, index)
                        )

                    logger.info(f"已连接跨寄存器控件 {widget_name} 的信号")

        except Exception as e:
            logger.error(f"连接跨寄存器控件信号失败: {str(e)}")

    def _connect_mux_widget_signals(self):
        """连接MUX控件的信号，实现实时计算"""
        try:
            # 连接PLL1NclkMux控件
            if hasattr(self.ui, "PLL1NclkMux"):
                self.ui.PLL1NclkMux.currentIndexChanged.connect(self._on_pll1_mux_changed)
                logger.info("已连接PLL1NclkMux信号")

            # 连接PLL2RclkMux控件
            if hasattr(self.ui, "PLL2RclkMux"):
                self.ui.PLL2RclkMux.currentIndexChanged.connect(self._on_mux_changed)
                logger.info("已连接PLL2RclkMux信号")

            # 连接PLL2NclkMux控件
            if hasattr(self.ui, "PLL2NclkMux"):
                self.ui.PLL2NclkMux.currentIndexChanged.connect(self._on_mux_changed)
                logger.info("已连接PLL2NclkMux信号")

            # 连接FBMUX控件
            if hasattr(self.ui, "FBMUX"):
                self.ui.FBMUX.currentIndexChanged.connect(self._on_mux_changed)
                self.ui.FBMUX.currentIndexChanged.connect(self._update_pll2cin_value_safe)
                logger.info("已连接FBMUX信号")

            # 连接FBMuxEn控件（反馈多路复用器使能）
            if hasattr(self.ui, "FBMuxEn"):
                self.ui.FBMuxEn.stateChanged.connect(self._on_mux_changed)
                self.ui.FBMuxEn.stateChanged.connect(self._update_pll2cin_visibility)
                logger.info("已连接FBMuxEn信号")

            logger.info("MUX控件信号连接完成")

        except Exception as e:
            logger.error(f"连接MUX控件信号时出错: {str(e)}")

    def _on_pll1_mux_changed(self):
        """当PLL1 NCLK MUX控件值改变时的处理函数"""
        try:
            logger.info("🔄 检测到PLL1 NCLK MUX值变化，进行PLL1NDivider自动调整")

            # 获取当前的PLL1 NCLK MUX值
            pll1_nclk_mux = 0
            if hasattr(self.ui, "PLL1NclkMux"):
                if hasattr(self.ui.PLL1NclkMux, 'currentData'):
                    pll1_nclk_mux = self.ui.PLL1NclkMux.currentData() or 0
                else:
                    pll1_nclk_mux = self.ui.PLL1NclkMux.currentIndex()
                logger.info(f"🔍 当前PLL1 NCLK MUX值: {pll1_nclk_mux}")

            # 根据不同的MUX选择调整PLL1NDivider
            self._adjust_pll1_n_divider_for_mux(pll1_nclk_mux)

            # 延迟一点时间再计算，避免频繁计算
            QtCore.QTimer.singleShot(50, self.calculate_output_frequencies)

        except Exception as e:
            logger.error(f"处理PLL1 MUX控件变化时出错: {str(e)}")

    def _on_mux_changed(self):
        """当MUX控件值改变时的处理函数"""
        try:
            logger.debug("检测到MUX控件值变化，重新计算频率")

            # 延迟一点时间再计算，避免频繁计算
            QtCore.QTimer.singleShot(50, self.calculate_output_frequencies)

        except Exception as e:
            logger.error(f"处理MUX控件变化时出错: {str(e)}")

    def _adjust_pll1_n_divider_for_mux(self, pll1_nclk_mux):
        """根据PLL1 NCLK MUX的选择自动调整PLL1NDivider以保持PLL1锁定

        PLL1基本公式: PLL1_PFD_Freq × PLL1NDivider = PLL1_Feedback_Freq

        Args:
            pll1_nclk_mux: PLL1 NCLK MUX的值 (0=OSCin, 1=Feedback Mux, 2=PLL2 Prescaler)
        """
        try:
            if not hasattr(self.ui, "PLL1NDivider"):
                logger.warning("PLL1NDivider控件不存在，无法进行自动调整")
                return

            # 获取当前PLL1 PFD频率
            pll1_pfd_freq = 0.0
            if hasattr(self.ui, "PLL1PFDFreq"):
                pll1_pfd_text = self.ui.PLL1PFDFreq.text()
                try:
                    pll1_pfd_freq = float(pll1_pfd_text) if pll1_pfd_text else 0.0
                except ValueError:
                    pll1_pfd_freq = 0.0

            if pll1_pfd_freq <= 0:
                logger.warning("PLL1 PFD频率为0或无效，无法计算PLL1NDivider")
                return

            # 获取当前PLL1NDivider值作为参考
            current_n_divider = self.ui.PLL1NDivider.value()
            logger.info(f"📊 当前PLL1NDivider: {current_n_divider}")

            # 根据不同的MUX选择计算目标反馈频率
            target_feedback_freq = 0.0
            mux_description = ""

            if pll1_nclk_mux == 0:
                # OSCin模式：PLL1NDivider的输入频率直接来自OSCin
                # 在OSCin模式下，PLL1NDivider的作用是将OSCin频率分频到PLL1_PFD_Freq
                # 公式: OSCin频率 / PLL1NDivider = PLL1_PFD_Freq
                # 因此: PLL1NDivider = OSCin频率 / PLL1_PFD_Freq
                
                # 获取OSCin频率
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 122.88
                
                if oscin_freq > 0:
                    # 在OSCin模式下，target_feedback_freq实际上就是OSCin频率
                    target_feedback_freq = oscin_freq
                    mux_description = "OSCin"
                    logger.info(f"OSCin模式: OSCin频率={oscin_freq:.6f} MHz, PLL1NDivider输入频率={target_feedback_freq:.6f} MHz")
                else:
                    # 如果OSCin频率无效，使用默认值
                    target_feedback_freq = 122.88  # 默认OSCin频率
                    mux_description = "OSCin(默认)"
                    logger.warning(f"OSCin频率无效，使用默认值: {target_feedback_freq} MHz")
            elif pll1_nclk_mux == 1:
                # Feedback Mux模式：反馈频率来自时钟输出
                target_feedback_freq = self._get_feedback_mux_frequency()
                mux_description = "Feedback Mux"
            elif pll1_nclk_mux == 2:
                # PLL2 Prescaler模式：使用特殊公式 VCODist / PLL2PRE / PLL1NDivider = PLL1PFD
                # 因此 PLL1NDivider = VCODist / (PLL2PRE × PLL1PFD)
                vco_dist_freq = self._get_vco_dist_frequency()
                pll2_prescaler = self._get_pll2_prescaler_value()

                if vco_dist_freq > 0 and pll2_prescaler > 0:
                    # 直接计算PLL1NDivider，不使用target_feedback_freq
                    suggested_n_divider_pll2pre = vco_dist_freq / (pll2_prescaler * pll1_pfd_freq)
                    rounded_suggestion_pll2pre = max(2, min(16383, round(suggested_n_divider_pll2pre)))

                    logger.info("🎯 PLL2 Prescaler模式特殊计算:")
                    logger.info("  公式: VCODist / (PLL2PRE × PLL1PFD) = PLL1NDivider")
                    logger.info(f"  VCODist频率: {vco_dist_freq:.6f} MHz")
                    logger.info(f"  PLL2 Prescaler: {pll2_prescaler}")
                    logger.info(f"  PLL1 PFD频率: {pll1_pfd_freq:.6f} MHz")
                    logger.info(f"  计算结果: {vco_dist_freq:.6f} / ({pll2_prescaler} × {pll1_pfd_freq:.6f}) = {suggested_n_divider_pll2pre:.6f}")
                    logger.info(f"  建议PLL1NDivider: {rounded_suggestion_pll2pre}")

                    # 检查是否需要调整
                    if abs(current_n_divider - rounded_suggestion_pll2pre) > 1:
                        logger.info(f"✅ PLL2 Prescaler模式自动调整PLL1NDivider: {current_n_divider} -> {rounded_suggestion_pll2pre}")
                        self.ui.PLL1NDivider.setValue(rounded_suggestion_pll2pre)

                        # 验证调整后的效果
                        verification_result = vco_dist_freq / (pll2_prescaler * rounded_suggestion_pll2pre)
                        logger.info(f"📈 调整后验证: {vco_dist_freq:.6f} / ({pll2_prescaler} × {rounded_suggestion_pll2pre}) = {verification_result:.6f} MHz")
                        logger.info(f"📊 与PLL1PFD频率对比: {verification_result:.6f} vs {pll1_pfd_freq:.6f} MHz")

                        if abs(verification_result - pll1_pfd_freq) / pll1_pfd_freq < 0.01:
                            logger.info("🎉 PLL2 Prescaler模式PLL1NDivider调整成功")
                        else:
                            logger.warning("⚠️ PLL2 Prescaler模式调整后仍有误差")
                    else:
                        logger.info("ℹ️ PLL2 Prescaler模式当前PLL1NDivider值已接近最优")

                    return  # PLL2 Prescaler模式特殊处理完成，直接返回
                else:
                    logger.warning(f"PLL2 Prescaler模式参数无效: VCODist={vco_dist_freq}, PLL2PRE={pll2_prescaler}")
                    return
            else:
                logger.warning(f"未知的PLL1 NCLK MUX值: {pll1_nclk_mux}")
                return

            if target_feedback_freq <= 0:
                logger.warning(f"目标反馈频率为0或无效 (MUX={mux_description})，无法计算PLL1NDivider")
                return

            # 计算建议的PLL1NDivider值
            # 公式: PLL1NDivider = PLL1_Feedback_Freq / PLL1_PFD_Freq
            suggested_n_divider = target_feedback_freq / pll1_pfd_freq
            rounded_suggestion = max(2, min(16383, round(suggested_n_divider)))  # 限制在有效范围内

            logger.info("🎯 PLL1NDivider自动调整分析:")
            logger.info(f"  PLL1 NCLK MUX: {pll1_nclk_mux} ({mux_description})")
            logger.info(f"  PLL1 PFD频率: {pll1_pfd_freq:.6f} MHz")
            logger.info(f"  目标反馈频率: {target_feedback_freq:.6f} MHz")
            logger.info(f"  当前PLL1NDivider: {current_n_divider}")
            logger.info(f"  建议PLL1NDivider: {rounded_suggestion} (精确值: {suggested_n_divider:.6f})")

            # 检查是否需要调整
            if abs(current_n_divider - rounded_suggestion) > 1:  # 允许1的误差
                logger.info(f"✅ 自动调整PLL1NDivider: {current_n_divider} -> {rounded_suggestion}")
                self.ui.PLL1NDivider.setValue(rounded_suggestion)

                # 验证调整后的效果
                new_feedback_freq = pll1_pfd_freq * rounded_suggestion
                logger.info(f"📈 调整后预期反馈频率: {new_feedback_freq:.6f} MHz (目标: {target_feedback_freq:.6f} MHz)")

                if abs(new_feedback_freq - target_feedback_freq) / target_feedback_freq < 0.01:  # 1%误差内
                    logger.info("🎉 PLL1NDivider调整成功，预期可保持PLL1锁定")
                else:
                    logger.warning("⚠️ PLL1NDivider调整后仍有较大误差，可能需要手动微调")
            else:
                logger.info("ℹ️ 当前PLL1NDivider值已接近最优，无需调整")

        except Exception as e:
            logger.error(f"调整PLL1NDivider时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _calculate_pll1_n_divider_realtime(self):
        """实时计算PLL1 N分频器值

        根据当前的PLL1 NCLK MUX选择和实时的反馈频率计算PLL1NDivider
        """
        try:
            logger.info("🔍 开始实时计算PLL1 N分频器...")

            if not hasattr(self.ui, "PLL1NDivider"):
                logger.warning("❌ PLL1NDivider控件不存在")
                return

            # 获取PLL1 PFD频率
            pll1_pfd_freq = self._get_float_from_lineedit(self.ui.PLL1PFDFreq)
            logger.info(f"📊 PLL1 PFD频率: {pll1_pfd_freq} MHz")

            if pll1_pfd_freq <= 0:
                logger.warning("❌ PLL1 PFD频率无效")
                return

            # 获取当前PLL1 NCLK MUX值
            pll1_nclk_mux = self._get_pll1_nclk_mux_value()
            logger.info(f"📊 当前PLL1 NCLK MUX: {pll1_nclk_mux}")

            # 根据PLL1 NCLK MUX选择获取输入频率
            input_freq = 0.0
            
            if pll1_nclk_mux == 0:
                # OSCin模式：直接使用OSCin频率
                input_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 122.88
                logger.info(f"📊 OSCin模式 - OSCin频率: {input_freq} MHz")
            elif pll1_nclk_mux == 2:
                # PLL2 Prescaler模式：使用VCODistFreq / PLL2Prescaler作为输入频率
                vco_dist_freq = self._get_vco_dist_frequency()
                pll2_prescaler = self._get_pll2_prescaler_value()
                
                if vco_dist_freq > 0 and pll2_prescaler > 0:
                    input_freq = vco_dist_freq / pll2_prescaler
                    logger.info(f"📊 PLL2 Prescaler模式 - VCODistFreq: {vco_dist_freq} MHz, PLL2Prescaler: {pll2_prescaler}")
                    logger.info(f"📊 计算输入频率: {vco_dist_freq} / {pll2_prescaler} = {input_freq} MHz")
                else:
                    logger.warning(f"❌ PLL2 Prescaler模式参数无效: VCODist={vco_dist_freq}, PLL2PRE={pll2_prescaler}")
                    return
            else:
                # Feedback Mux模式：使用反馈频率
                logger.info("🔍 获取实时反馈频率...")
                input_freq = self._calculate_current_feedback_frequency()
                logger.info(f"📊 实时反馈频率: {input_freq} MHz")

            if input_freq <= 0:
                logger.warning("❌ 输入频率无效")
                return

            # 计算PLL1 N分频器值
            pll1_n_divider = input_freq / pll1_pfd_freq

            logger.info(f"🧮 计算公式: {input_freq} / {pll1_pfd_freq} = {pll1_n_divider}")
            logger.info(f"✅ 计算得到的PLL1 N分频器: {pll1_n_divider}")

            # 检查当前控件值
            current_value = self._get_float_from_spinbox(self.ui.PLL1NDivider)
            logger.info(f"📊 当前控件值: {current_value}")

            # 四舍五入到整数
            rounded_value = round(pll1_n_divider)

            # 如果差异较大，则更新控件
            if abs(current_value - rounded_value) > 1:
                logger.info(f"🔄 更新PLL1NDivider: {current_value} -> {rounded_value}")
                self._set_float_to_spinbox(self.ui.PLL1NDivider, rounded_value)
            else:
                logger.info(f"ℹ️ PLL1NDivider值已接近最优: {current_value}")

        except Exception as e:
            logger.error(f"实时计算PLL1 N分频器时出错: {str(e)}")

    def _get_vco_dist_frequency(self):
        """获取VCO Dist频率

        Returns:
            float: VCO Dist频率值
        """
        try:
            if hasattr(self.ui, "VCODistFreq"):
                vco_dist_text = self.ui.VCODistFreq.text()
                try:
                    return float(vco_dist_text) if vco_dist_text else 0.0
                except ValueError:
                    return 0.0
            return 0.0
        except Exception as e:
            logger.error(f"获取VCO Dist频率时出错: {str(e)}")
            return 0.0

    def _update_pll2cin_visibility(self):
        """根据FBMuxEn状态更新PLL2Cin控件的可见性"""
        try:
            if not hasattr(self.ui, "PLL2Cin"):
                logger.warning("PLL2Cin控件不存在")
                return

            # 检查FBMuxEn是否选中
            fb_mux_enabled = False
            if hasattr(self.ui, "FBMuxEn"):
                fb_mux_enabled = self.ui.FBMuxEn.isChecked()
                logger.debug(f"FBMuxEn状态: {fb_mux_enabled}")
            else:
                logger.warning("FBMuxEn控件不存在")

            # 设置PLL2Cin控件的可见性
            self.ui.PLL2Cin.setVisible(fb_mux_enabled)
            if fb_mux_enabled:
                # 强制显示控件
                self.ui.PLL2Cin.show()
                self.ui.PLL2Cin.setHidden(False)
                self.ui.PLL2Cin.setEnabled(True)
                # 更新其数值
                self._update_pll2cin_value()
                logger.debug("PLL2Cin控件已显示并更新数值")
            else:
                # 强制隐藏控件
                self.ui.PLL2Cin.hide()
                self.ui.PLL2Cin.setHidden(True)
                logger.debug("PLL2Cin控件已隐藏")

            logger.debug(f"PLL2Cin可见性设置为: {fb_mux_enabled}")

        except Exception as e:
            logger.error(f"更新PLL2Cin可见性时出错: {str(e)}")

    def _update_pll2cin_value(self):
        """更新PLL2Cin控件的数值"""
        try:
            # 防止循环调用的标志
            if getattr(self, '_updating_from_sysref_signal', False):
                logger.debug("正在处理SYSREF信号更新，跳过PLL2Cin值更新以避免循环")
                return

            if not hasattr(self.ui, "PLL2Cin"):
                logger.warning("PLL2Cin控件不存在")
                return

            # 获取FBMUX的当前值 - 添加详细调试和文本匹配
            fb_mux_value = 0
            if hasattr(self.ui, "FBMUX"):
                if hasattr(self.ui.FBMUX, 'currentData'):
                    data_value = self.ui.FBMUX.currentData()
                    index_value = self.ui.FBMUX.currentIndex()
                    current_text = self.ui.FBMUX.currentText()

                    # 优先使用currentData，如果为None则使用currentIndex
                    fb_mux_value = data_value if data_value is not None else index_value

                    # 如果仍然无法确定，通过文本匹配
                    if current_text:
                        if "SYSREF" in current_text.upper() or "DIVIDER" in current_text.upper():
                            fb_mux_value = 2
                            logger.info(f"【PLL2Cin调试】通过文本匹配确定FBMUX为SYSREF: '{current_text}' -> 2")
                        elif "CLKOUT8" in current_text.upper() or "CLK8" in current_text.upper():
                            fb_mux_value = 1
                            logger.info(f"【PLL2Cin调试】通过文本匹配确定FBMUX为CLKout8: '{current_text}' -> 1")
                        elif "CLKOUT6" in current_text.upper() or "CLK6" in current_text.upper():
                            fb_mux_value = 0
                            logger.info(f"【PLL2Cin调试】通过文本匹配确定FBMUX为CLKout6: '{current_text}' -> 0")

                    logger.info(f"【PLL2Cin调试】FBMUX状态 - currentData: {data_value}, currentIndex: {index_value}, currentText: '{current_text}', 最终使用值: {fb_mux_value}")
                else:
                    fb_mux_value = self.ui.FBMUX.currentIndex()
                    current_text = self.ui.FBMUX.currentText()
                    logger.info(f"【PLL2Cin调试】FBMUX currentIndex: {fb_mux_value}, currentText: '{current_text}'")
            else:
                logger.warning("FBMUX控件不存在")

            frequency = 0.0

            if fb_mux_value == 0:  # CLKout6
                frequency = self._get_clkout6_frequency()
                logger.info(f"【PLL2Cin调试】使用CLKout6频率: {frequency:.5f} MHz")
            elif fb_mux_value == 1:  # CLKout8
                frequency = self._get_clkout8_frequency()
                logger.info(f"【PLL2Cin调试】使用CLKout8频率: {frequency:.5f} MHz")
            elif fb_mux_value == 2:  # SYSREF
                frequency = self._get_sysref_frequency()
                logger.info(f"【PLL2Cin调试】使用SYSREF频率: {frequency:.5f} MHz")
            else:
                logger.warning(f"【PLL2Cin调试】未知的FBMUX值: {fb_mux_value}")

            # 更新PLL2Cin控件的显示值
            old_value = self.ui.PLL2Cin.text()
            self.ui.PLL2Cin.setText(f"{frequency:.5f}")
            self.ui.PLL2Cin.setReadOnly(True)  # 设置为只读
            logger.info(f"【PLL2Cin调试】PLL2Cin更新: '{old_value}' -> '{frequency:.5f}' MHz (FBMUX={fb_mux_value})")

        except Exception as e:
            logger.error(f"更新PLL2Cin数值时出错: {str(e)}")

    def _update_pll2cin_value_safe(self):
        """安全地更新PLL2Cin控件的数值（防循环版本）"""
        try:
            # 防止循环调用的标志
            if getattr(self, '_updating_from_sysref_signal', False):
                logger.debug("正在处理SYSREF信号更新，跳过PLL2Cin值更新以避免循环")
                return

            # 调用原始的更新方法
            self._update_pll2cin_value()

        except Exception as e:
            logger.error(f"安全更新PLL2Cin数值时出错: {str(e)}")



    def _connect_clk_output_divider_signals(self):
        """连接时钟输出窗口的分频器信号，实现跨窗口实时计算

        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info("🔗 尝试连接时钟输出分频器信号...")

            # 尝试获取时钟输出窗口实例
            main_window = self._get_main_window()
            if main_window:
                clk_outputs_handler = None

                # 方法1: 通过clk_output_window属性访问（传统方式）
                if hasattr(main_window, 'clk_output_window') and main_window.clk_output_window:
                    clk_outputs_handler = main_window.clk_output_window
                    logger.info("✅ 通过clk_output_window属性找到时钟输出窗口实例")

                # 方法2: 通过插件系统访问（现代方式）
                elif hasattr(main_window, 'plugin_integration_service'):
                    plugin_service = main_window.plugin_integration_service
                    if hasattr(plugin_service, 'get_plugin_window'):
                        clk_outputs_handler = plugin_service.get_plugin_window("时钟输出")
                        if clk_outputs_handler:
                            logger.info("✅ 通过插件系统找到时钟输出窗口实例")

                # 方法3: 通过查找子窗口的方式（备用方法）
                if not clk_outputs_handler:
                    for child in main_window.findChildren(QtCore.QObject):
                        if hasattr(child, 'ui') and hasattr(child.ui, 'DCLK6_7DIV'):
                            clk_outputs_handler = child
                            logger.info("✅ 通过子窗口查找找到时钟输出窗口实例")
                            break

                if clk_outputs_handler:
                    signals_connected = 0

                    # 连接DCLK6_7DIV控件信号
                    if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                        clk_outputs_handler.ui.DCLK6_7DIV.valueChanged.connect(self._on_clk_divider_changed)
                        logger.info("✅ 已连接DCLK6_7DIV信号")
                        signals_connected += 1
                    else:
                        logger.warning("❌ DCLK6_7DIV控件不存在")

                    # 连接DCLK8_9DIV控件信号
                    if hasattr(clk_outputs_handler.ui, 'DCLK8_9DIV'):
                        clk_outputs_handler.ui.DCLK8_9DIV.valueChanged.connect(self._on_clk_divider_changed)
                        logger.info("✅ 已连接DCLK8_9DIV信号")
                        signals_connected += 1
                    else:
                        logger.warning("❌ DCLK8_9DIV控件不存在")

                    if signals_connected > 0:
                        logger.info("🎉 时钟输出分频器信号连接完成")
                        return True
                    else:
                        logger.warning("❌ 没有成功连接任何信号")
                        return False
                else:
                    # 使用防重复警告机制
                    self._log_clk_output_connection_warning()
                    # 设置定时器延迟重试
                    QtCore.QTimer.singleShot(1000, self._retry_connect_clk_output_signals)
                    return False
            else:
                logger.warning("⚠️ 主窗口未找到")
                return False

        except Exception as e:
            logger.error(f"连接时钟输出分频器信号时出错: {str(e)}")
            return False

    def _retry_connect_clk_output_signals(self):
        """重试连接时钟输出信号"""
        try:
            # 限制重试次数，避免无限循环
            if not hasattr(self, '_retry_count'):
                self._retry_count = 0

            self._retry_count += 1
            if self._retry_count > 10:  # 最多重试10次
                logger.warning("⚠️ 重试连接时钟输出信号次数过多，停止重试")
                return

            logger.info(f"🔄 重试连接时钟输出分频器信号... (第{self._retry_count}次)")
            success = self._connect_clk_output_divider_signals()

            if success:
                logger.info("✅ 重试连接成功，停止重试")
                self._retry_count = 0  # 重置计数器

        except Exception as e:
            logger.error(f"重试连接时钟输出信号时出错: {str(e)}")

    def _on_clk_divider_changed(self):
        """当时钟输出分频器值改变时的处理函数"""
        try:
            logger.info("🔄 检测到时钟输出分频器值变化，重新计算频率")

            # 检查当前的PLL2NclkMux状态
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()
            logger.info(f"🔍 当前PLL2NclkMux值: {pll2_nclk_mux}")

            if pll2_nclk_mux == 1:
                logger.info("✅ 检测到反馈模式，将进行PLL2NDivider自动调整")

                # 获取当前的反馈频率
                feedback_freq = self._get_feedback_mux_frequency()
                logger.info(f"📊 当前反馈频率: {feedback_freq:.6f} MHz")

                # 获取当前的PLL2NDivider值
                current_n_divider = self._get_pll2_n_divider_value()
                logger.info(f"📊 当前PLL2NDivider: {current_n_divider}")
            else:
                logger.info("ℹ️ 非反馈模式，不进行PLL2NDivider自动调整")

            # 延迟一点时间再计算，避免频繁计算
            QtCore.QTimer.singleShot(50, self.calculate_output_frequencies)

        except Exception as e:
            logger.error(f"处理时钟输出分频器变化时出错: {str(e)}")

    def force_reconnect_clk_output_signals(self):
        """强制重新连接时钟输出信号（用于调试）"""
        try:
            logger.info("🔧 强制重新连接时钟输出分频器信号...")
            self._retry_count = 0  # 重置重试计数器
            success = self._connect_clk_output_divider_signals()
            if success:
                logger.info("✅ 强制重新连接成功")
            else:
                logger.warning("❌ 强制重新连接失败")
            return success
        except Exception as e:
            logger.error(f"强制重新连接时出错: {str(e)}")
            return False

    def test_pll2_ndivider_control(self):
        """测试PLL2NDivider控件的状态和功能（用于调试）"""
        try:
            logger.info("🧪 测试PLL2NDivider控件状态...")

            if hasattr(self.ui, "PLL2NDivider"):
                widget = self.ui.PLL2NDivider
                logger.info("📊 PLL2NDivider控件信息:")
                logger.info(f"  当前值: {widget.value()}")
                logger.info(f"  最小值: {widget.minimum()}")
                logger.info(f"  最大值: {widget.maximum()}")
                logger.info(f"  是否启用: {widget.isEnabled()}")
                logger.info(f"  是否只读: {widget.isReadOnly()}")
                logger.info(f"  是否可见: {widget.isVisible()}")

                # 尝试设置一个测试值
                old_value = widget.value()
                test_value = old_value + 1 if old_value < widget.maximum() else old_value - 1
                logger.info(f"🔧 尝试将PLL2NDivider从 {old_value} 改为 {test_value}")

                widget.setValue(test_value)
                new_value = widget.value()

                if new_value == test_value:
                    logger.info("✅ PLL2NDivider控件可以正常修改")
                else:
                    logger.warning(f"❌ PLL2NDivider控件修改失败，期望: {test_value}, 实际: {new_value}")

                # 恢复原值
                widget.setValue(old_value)

            else:
                logger.error("❌ PLL2NDivider控件不存在")

        except Exception as e:
            logger.error(f"测试PLL2NDivider控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def force_enable_pll2_ndivider(self):
        """强制启用PLL2NDivider控件（用于调试）"""
        try:
            logger.info("🔧 强制启用PLL2NDivider控件...")

            if hasattr(self.ui, "PLL2NDivider"):
                widget = self.ui.PLL2NDivider

                # 记录当前状态
                logger.info(f"修改前状态: 启用={widget.isEnabled()}, 只读={widget.isReadOnly()}")

                # 强制启用
                widget.setEnabled(True)
                if hasattr(widget, 'setReadOnly'):
                    widget.setReadOnly(False)

                # 设置样式确保可见
                widget.setStyleSheet("")  # 清除可能的禁用样式

                # 强制刷新
                widget.update()
                widget.repaint()

                # 记录修改后状态
                logger.info(f"修改后状态: 启用={widget.isEnabled()}, 只读={widget.isReadOnly()}")

                logger.info("✅ PLL2NDivider控件强制启用完成")

            else:
                logger.error("❌ PLL2NDivider控件不存在")

        except Exception as e:
            logger.error(f"强制启用PLL2NDivider控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _on_sysref_freq_updated(self, sysref_freq):
        """处理SYSREF频率更新信号，同步更新PLL2Cin值

        当同步系统参考窗口的SyncSysrefFreq1值更新时，如果FBMUX选择为SYSREF DIVIDER，
        则需要同步更新PLL2Cin的显示值

        Args:
            sysref_freq (float): 更新后的SYSREF频率值
        """
        try:
            # 防止循环调用的标志
            if getattr(self, '_updating_from_sysref_signal', False):
                logger.debug("正在处理SYSREF信号更新，跳过重复调用")
                return

            logger.info(f"【PLL窗口】收到SYSREF频率更新信号: {sysref_freq:.5f} MHz")

            # 检查PLL2Cin控件是否存在且可见
            if not hasattr(self.ui, "PLL2Cin") or not self.ui.PLL2Cin.isVisible():
                logger.debug("PLL2Cin控件不存在或不可见，跳过更新")
                return

            # 检查FBMuxEn是否启用
            if not hasattr(self.ui, "FBMuxEn") or not self.ui.FBMuxEn.isChecked():
                logger.debug("FBMuxEn未启用，跳过PLL2Cin更新")
                return

            # 检查FBMUX是否选择为SYSREF DIVIDER
            fb_mux_value = 0
            if hasattr(self.ui, "FBMUX"):
                if hasattr(self.ui.FBMUX, 'currentData'):
                    fb_mux_value = self.ui.FBMUX.currentData() or 0
                else:
                    fb_mux_value = self.ui.FBMUX.currentIndex()

                # 通过文本匹配确认是否为SYSREF
                current_text = self.ui.FBMUX.currentText()
                if "SYSREF" in current_text.upper() or "DIVIDER" in current_text.upper():
                    fb_mux_value = 2

            if fb_mux_value == 2:  # SYSREF DIVIDER
                # 设置标志防止循环
                self._updating_from_sysref_signal = True

                try:
                    # 更新PLL2Cin显示值
                    old_value = self.ui.PLL2Cin.text()
                    self.ui.PLL2Cin.setText(f"{sysref_freq:.5f}")
                    logger.info(f"【PLL窗口】PLL2Cin同步更新: '{old_value}' -> '{sysref_freq:.5f}' MHz (来自SYSREF频率更新)")

                    # 只更新显示值，不触发完整的频率重新计算，避免循环
                    # QtCore.QTimer.singleShot(100, self.calculate_output_frequencies)  # 注释掉避免循环
                    logger.debug("PLL2Cin值已更新，跳过频率重新计算以避免循环")

                finally:
                    # 确保标志被重置
                    self._updating_from_sysref_signal = False
            else:
                logger.debug(f"FBMUX未选择SYSREF DIVIDER (当前值: {fb_mux_value})，跳过PLL2Cin更新")

        except Exception as e:
            logger.error(f"处理SYSREF频率更新时出错: {str(e)}")
            # 确保在异常情况下也重置标志
            self._updating_from_sysref_signal = False

    def _sync_frequency_widgets(self, source_widget):
        """同步ExternalVCXOFreq和OSCinFreq控件的值

        Args:
            source_widget: 触发同步的源控件名称 ("OSCinFreq" 或 "ExternalVCXOFreq")
        """
        try:
            # 防止递归调用的标志
            if hasattr(self, '_syncing_frequency_widgets') and self._syncing_frequency_widgets:
                return

            self._syncing_frequency_widgets = True

            # 获取源控件的值
            source_value = ""
            if source_widget == "OSCinFreq" and hasattr(self.ui, "OSCinFreq"):
                source_value = self.ui.OSCinFreq.text()
                target_widget_name = "ExternalVCXOFreq"
            elif source_widget == "ExternalVCXOFreq" and hasattr(self.ui, "ExternalVCXOFreq"):
                source_value = self.ui.ExternalVCXOFreq.text()
                target_widget_name = "OSCinFreq"
            else:
                logger.warning(f"未知的源控件: {source_widget}")
                return

            # 更新目标控件的值
            if hasattr(self.ui, target_widget_name):
                target_widget = getattr(self.ui, target_widget_name)
                if target_widget.text() != source_value:
                    target_widget.setText(source_value)
                    logger.debug(f"同步频率控件: {source_widget} -> {target_widget_name}, 值: {source_value}")

                    # 缓存更新的值
                    self._cache_lineedit_value(target_widget_name, source_value)

            # 缓存源控件的值
            self._cache_lineedit_value(source_widget, source_value)

        except Exception as e:
            logger.error(f"同步频率控件时出错: {str(e)}")
        finally:
            # 重置同步标志
            self._syncing_frequency_widgets = False

    def _perform_initial_calculations(self):
        """执行初始计算，确保所有频率显示控件都有正确的初始值"""
        try:
            logger.info("开始执行初始频率计算...")

            # 1. 初始化FreFin
            self._initialize_freq_fin()

            # 2. 初始化PLL1RDividerSetting
            self._initialize_pll1r_divider()

            # 3. 验证PLL2NDivider
            self._verify_pll2n_divider()

            # 4. 初始化PLL2RDivider
            self._initialize_pll2r_divider()

            # 5. 执行基础频率计算，确保VCODistFreq等值可用
            logger.info("执行基础频率计算，为PLL2Cin初始化做准备...")
            self._calculate_basic_pll_frequencies()

            # 6. 强制更新PLL2Cin值，确保反馈频率可用
            logger.info("强制更新PLL2Cin值...")
            if hasattr(self.ui, "FBMuxEn") and self.ui.FBMuxEn.isChecked():
                self._update_pll2cin_value()
                logger.info("PLL2Cin值已更新")
            else:
                logger.info("FBMuxEn未启用，跳过PLL2Cin更新")

            # 7. 初始化PLL1NDivider（现在PLL2Cin值应该已经可用）
            self._initialize_pll1n_divider()

            # 8. 诊断当前控件状态
            self._diagnose_pfd_calculation_inputs()

            # 9. 执行完整的频率计算
            self.calculate_output_frequencies()

            # 10. 初始化VCO频率范围验证
            QtCore.QTimer.singleShot(200, self._validate_vco_dist_freq_range)

            # 11. 初始化Fin0PD控件状态（根据当前VCO模式）
            self._initialize_fin0pd_state()

            logger.info("初始频率计算完成")

        except Exception as e:
            logger.error(f"执行初始计算时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _initialize_fin0pd_state(self):
        """初始化Fin0PD控件状态（根据当前VCO模式）"""
        try:
            logger.info("开始初始化Fin0PD控件状态...")

            # 检查必要的控件是否存在
            if not (hasattr(self.ui, "comboVcoMode") and hasattr(self.ui, "Fin0PD")):
                if not hasattr(self.ui, "comboVcoMode"):
                    logger.warning("comboVcoMode控件不存在，跳过Fin0PD初始化")
                if not hasattr(self.ui, "Fin0PD"):
                    logger.warning("Fin0PD控件不存在，跳过Fin0PD初始化")
                return

            # 获取当前VCO模式
            vco_mode = 0
            if hasattr(self.ui.comboVcoMode, 'currentData'):
                vco_mode = self.ui.comboVcoMode.currentData() or 0
            else:
                vco_mode = self.ui.comboVcoMode.currentIndex()

            logger.info(f"初始化时检测到VCO模式: {vco_mode}")

            # 根据VCO模式设置Fin0PD状态
            self._update_fin0pd_for_vco_mode(vco_mode)

            # 根据VCO模式设置Fin0Freq只读状态
            self._update_fin0freq_readonly_state(vco_mode)

            logger.info("Fin0PD控件状态初始化完成")

        except Exception as e:
            logger.error(f"初始化Fin0PD控件状态时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _diagnose_pfd_calculation_inputs(self):
        """诊断PFD计算的输入参数"""
        try:
            logger.info("=== PFD计算诊断开始 ===")

            # 检查OSCin频率
            if hasattr(self.ui, "OSCinFreq"):
                oscin_text = self.ui.OSCinFreq.text()
                oscin_value = self._get_float_from_lineedit(self.ui.OSCinFreq)
                logger.info(f"OSCin频率控件: 文本='{oscin_text}', 数值={oscin_value}")
            else:
                logger.warning("OSCinFreq控件不存在")

            # 检查FreFin频率
            if hasattr(self.ui, "FreFin"):
                frefin_text = self.ui.FreFin.text()
                frefin_value = self._get_float_from_lineedit(self.ui.FreFin)
                logger.info(f"FreFin频率控件: 文本='{frefin_text}', 数值={frefin_value}")
            else:
                logger.warning("FreFin控件不存在")

            # 检查PLL1 R分频器
            if hasattr(self.ui, "PLL1RDividerSetting"):
                pll1r_value = self.ui.PLL1RDividerSetting.value()
                logger.info(f"PLL1 R分频器: {pll1r_value}")
            else:
                logger.warning("PLL1RDividerSetting控件不存在")

            # 检查PLL2 R分频器
            if hasattr(self.ui, "PLL2RDivider"):
                pll2r_value = self.ui.PLL2RDivider.value()
                logger.info(f"PLL2 R分频器: {pll2r_value}")
            else:
                logger.warning("PLL2RDivider控件不存在")

            # 检查Doubler
            if hasattr(self.ui, "Doubler"):
                doubler_index = self.ui.Doubler.currentIndex()
                doubler_value = doubler_index + 1
                logger.info(f"Doubler: 索引={doubler_index}, 值={doubler_value}")
            else:
                logger.warning("Doubler控件不存在")

            # 检查PLL掉电状态
            if hasattr(self.ui, "PLL1PD"):
                pll1_pd = self.ui.PLL1PD.isChecked()
                logger.info(f"PLL1掉电状态: {pll1_pd}")
            else:
                logger.warning("PLL1PD控件不存在")

            if hasattr(self.ui, "PLL2PD"):
                pll2_pd = self.ui.PLL2PD.isChecked()
                logger.info(f"PLL2掉电状态: {pll2_pd}")
            else:
                logger.warning("PLL2PD控件不存在")

            logger.info("=== PFD计算诊断结束 ===")

        except Exception as e:
            logger.error(f"PFD计算诊断时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _initialize_freq_fin(self):
        """初始化FreFin控件"""
        if hasattr(self.ui, "FreFin"):
            current_fin_freq = self.clkin_frequencies.get(self.current_clock_source, 122.88)
            self.ui.FreFin.setText(str(current_fin_freq))
            logger.info(f"设置FreFin为: {current_fin_freq} (基于当前时钟源: {self.current_clock_source})")

    def _initialize_pll1r_divider(self):
        """初始化PLL1RDividerSetting控件"""
        widget_name = "PLL1RDividerSetting"
        if not hasattr(self.ui, widget_name):
            return

        widget = getattr(self.ui, widget_name)
        default_divider = self.clkin_divider_values.get(self.current_clock_source, 120)

        # 尝试从RegisterManager获取值
        final_value, from_register = self._get_pll1r_value_from_register(widget_name, default_divider)

        # 确保值在控件范围内
        min_val, max_val = widget.minimum(), widget.maximum()
        final_value, was_clamped = self._clamp_value_to_range(final_value, min_val, max_val, widget_name)

        widget.setValue(final_value)
        source = "寄存器" if from_register and not was_clamped else "默认/修正值"
        logger.info(f"设置 {widget_name} 为: {final_value} (来自{source})")

    def _get_pll1r_value_from_register(self, widget_name, default_value):
        """从RegisterManager获取PLL1RDivider值"""
        if not self.register_manager:
            return default_value, False

        widget_info = self.widget_register_map.get(widget_name)
        if not widget_info:
            logger.info(f"{widget_name} 未在 widget_register_map 中定义，使用默认值: {default_value}")
            return default_value, False

        reg_addr = widget_info.get("register_addr")
        bit_def_name = widget_info.get("bit_def_name")

        # 对于PLL1RDividerSetting，bit_def_name可能需要从bits字段获取
        if "bit_def" in widget_info and "bits" in widget_info["bit_def"] and widget_info["bit_def"]["bits"]:
            bit_def_name = widget_info["bit_def"]["bits"][0]

        if reg_addr and bit_def_name:
            value, success = self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
            if success:
                return int(value), True

        logger.info(f"{widget_name} 无法从RegisterManager获取，使用默认值: {default_value}")
        return default_value, False

    def _verify_pll2n_divider(self):
        """验证PLL2NDivider控件"""
        if hasattr(self.ui, "PLL2NDivider"):
            widget = self.ui.PLL2NDivider
            if widget.value() < widget.minimum():
                logger.warning(f"PLL2NDivider 值 {widget.value()} 小于最小值，可能未正确初始化")
            else:
                logger.info(f"PLL2NDivider 初始化验证通过，当前值: {widget.value()}")

    def _initialize_pll2r_divider(self):
        """初始化PLL2RDivider控件"""
        widget_name = "PLL2RDivider"
        if not hasattr(self.ui, widget_name):
            return

        widget = getattr(self.ui, widget_name)
        default_value = 1
        min_val, max_val = 1, 127

        # 尝试从widget_register_map获取配置
        final_value, from_register = self._get_pll2r_value_from_config(widget_name, default_value, min_val, max_val)

        # 确保值在范围内
        final_value, was_clamped = self._clamp_value_to_range(final_value, min_val, max_val, widget_name)

        widget.setValue(final_value)
        source = "寄存器" if from_register and not was_clamped else "默认/修正值"
        logger.info(f"设置 {widget_name} 为: {final_value} (来自{source})")

    def _initialize_pll1n_divider(self):
        """初始化PLL1NDivider控件，根据当前PLL1 NCLK MUX设置计算合适的初始值"""
        try:
            if not hasattr(self.ui, "PLL1NDivider"):
                logger.warning("PLL1NDivider控件不存在，跳过初始化")
                return

            logger.info("开始初始化PLL1NDivider...")

            # 首先尝试从寄存器获取当前值
            current_value = self.ui.PLL1NDivider.value()
            logger.info(f"PLL1NDivider当前值: {current_value}")

            # 检查是否需要根据PLL1 NCLK MUX自动计算初始值
            if self._should_auto_calculate_pll1n_divider():
                logger.info("检测到需要自动计算PLL1NDivider初始值")

                # 先进行一次基础的频率计算，确保PLL1PFD有值
                self._calculate_basic_pll_frequencies()

                # 获取当前PLL1 NCLK MUX值
                pll1_nclk_mux = self._get_pll1_nclk_mux_value()
                logger.info(f"当前PLL1 NCLK MUX: {pll1_nclk_mux}")

                # 根据MUX值计算合适的PLL1NDivider
                self._calculate_initial_pll1n_divider(pll1_nclk_mux)
            else:
                logger.info("使用寄存器中的PLL1NDivider值，不进行自动计算")

        except Exception as e:
            logger.error(f"初始化PLL1NDivider时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _should_auto_calculate_pll1n_divider(self):
        """判断是否应该自动计算PLL1NDivider初始值

        Returns:
            bool: True表示需要自动计算，False表示使用现有值
        """
        try:
            # 检查是否有有效的输入频率
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
            frefin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0

            # 如果输入频率都为0，则不进行自动计算
            if oscin_freq <= 0 and frefin_freq <= 0:
                logger.info("输入频率为0，不进行PLL1NDivider自动计算")
                return False

            # 检查PLL1是否掉电
            if hasattr(self.ui, "PLL1PD") and self.ui.PLL1PD.isChecked():
                logger.info("PLL1处于掉电状态，不进行PLL1NDivider自动计算")
                return False

            return True

        except Exception as e:
            logger.error(f"判断是否自动计算PLL1NDivider时出错: {str(e)}")
            return False

    def _get_pll1_nclk_mux_value(self):
        """获取PLL1 NCLK MUX的当前值

        Returns:
            int: PLL1 NCLK MUX值 (0=OSCin, 1=Feedback Mux, 2=PLL2 Prescaler)
        """
        try:
            if hasattr(self.ui, "PLL1NclkMux"):
                if hasattr(self.ui.PLL1NclkMux, 'currentData'):
                    return self.ui.PLL1NclkMux.currentData() or 0
                else:
                    return self.ui.PLL1NclkMux.currentIndex()
            return 0  # 默认OSCin
        except Exception as e:
            logger.error(f"获取PLL1 NCLK MUX值时出错: {str(e)}")
            return 0

    def _calculate_basic_pll_frequencies(self):
        """计算基础的PLL频率，为PLL1NDivider初始化提供必要的数据"""
        try:
            logger.debug("计算基础PLL频率...")

            # 计算PLL1 PFD频率
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
            if fin_freq > 0:
                self._calculate_pll1_output(fin_freq)

            # 计算PLL2 PFD频率
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
            if oscin_freq > 0:
                self._calculate_pll2_output_with_source(oscin_freq)

        except Exception as e:
            logger.error(f"计算基础PLL频率时出错: {str(e)}")

    def _calculate_initial_pll1n_divider(self, pll1_nclk_mux):
        """根据PLL1 NCLK MUX值计算初始的PLL1NDivider值

        Args:
            pll1_nclk_mux: PLL1 NCLK MUX值
        """
        try:
            logger.info(f"根据PLL1 NCLK MUX({pll1_nclk_mux})计算初始PLL1NDivider值")

            # 获取PLL1 PFD频率
            pll1_pfd_freq = 0.0
            if hasattr(self.ui, "PLL1PFDFreq"):
                pll1_pfd_text = self.ui.PLL1PFDFreq.text()
                try:
                    pll1_pfd_freq = float(pll1_pfd_text) if pll1_pfd_text else 0.0
                except ValueError:
                    pll1_pfd_freq = 0.0

            if pll1_pfd_freq <= 0:
                logger.warning("PLL1 PFD频率为0，无法计算初始PLL1NDivider，使用默认值")
                return

            suggested_n_divider = 100  # 默认值
            calculation_info = ""

            if pll1_nclk_mux == 0:
                # OSCin模式：PLL1NDivider的输入频率直接来自OSCin
                # 公式: OSCin频率 / PLL1NDivider = PLL1_PFD_Freq
                # 因此: PLL1NDivider = OSCin频率 / PLL1_PFD_Freq
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
                if oscin_freq > 0:
                    # 在OSCin模式下，直接用OSCin频率除以PLL1_PFD_Freq
                    suggested_n_divider = oscin_freq / pll1_pfd_freq
                    calculation_info = f"OSCin模式: OSCin频率({oscin_freq}) / PLL1_PFD_Freq({pll1_pfd_freq}) = {suggested_n_divider:.2f}"
                else:
                    suggested_n_divider = 100  # 默认值
                    calculation_info = f"OSCin模式(OSCin频率无效，使用默认值): {suggested_n_divider}"

            elif pll1_nclk_mux == 1:
                # Feedback Mux模式：实时计算当前的反馈频率
                current_feedback_freq = self._calculate_current_feedback_frequency()
                if current_feedback_freq > 0:
                    suggested_n_divider = current_feedback_freq / pll1_pfd_freq
                    calculation_info = f"Feedback Mux模式(实时计算): {current_feedback_freq} / {pll1_pfd_freq} = {suggested_n_divider:.2f}"
                else:
                    # 如果无法计算当前反馈频率，使用经验默认值
                    suggested_n_divider = 240  # 经验值
                    calculation_info = f"Feedback Mux模式(无法计算，使用默认值): {suggested_n_divider}"

            elif pll1_nclk_mux == 2:
                # PLL2 Prescaler模式：使用特殊公式
                vco_dist_freq = self._get_vco_dist_frequency()
                pll2_prescaler = self._get_pll2_prescaler_value()

                if vco_dist_freq > 0 and pll2_prescaler > 0:
                    suggested_n_divider = vco_dist_freq / (pll2_prescaler * pll1_pfd_freq)
                    calculation_info = f"PLL2 Prescaler模式: {vco_dist_freq} / ({pll2_prescaler} × {pll1_pfd_freq}) = {suggested_n_divider:.2f}"

            # 限制在有效范围内
            rounded_suggestion = max(2, min(16383, round(suggested_n_divider)))

            logger.info("🎯 PLL1NDivider初始值计算:")
            logger.info(f"  {calculation_info}")
            logger.info(f"  建议初始值: {rounded_suggestion}")

            # 设置初始值
            if hasattr(self.ui, "PLL1NDivider"):
                current_value = self.ui.PLL1NDivider.value()
                if abs(current_value - rounded_suggestion) > 10:  # 如果差异较大才更新
                    self.ui.PLL1NDivider.setValue(rounded_suggestion)
                    logger.info(f"✅ 设置PLL1NDivider初始值: {current_value} -> {rounded_suggestion}")
                else:
                    logger.info(f"ℹ️ 当前PLL1NDivider值({current_value})已接近计算值({rounded_suggestion})，保持不变")

        except Exception as e:
            logger.error(f"计算初始PLL1NDivider值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_pll2cin_frequency(self):
        """获取PLL2Cin控件的频率值

        Returns:
            float: PLL2Cin控件显示的频率值
        """
        try:
            if hasattr(self.ui, "PLL2Cin"):
                pll2cin_text = self.ui.PLL2Cin.text()
                try:
                    return float(pll2cin_text) if pll2cin_text else 0.0
                except ValueError:
                    logger.warning(f"PLL2Cin控件值无法转换为数字: '{pll2cin_text}'")
                    return 0.0
            else:
                logger.warning("PLL2Cin控件不存在")
                return 0.0
        except Exception as e:
            logger.error(f"获取PLL2Cin频率时出错: {str(e)}")
            return 0.0

    def _calculate_current_feedback_frequency(self):
        """实时计算当前的反馈频率

        根据当前的FBMUX选择和相关参数，实时计算反馈频率，
        而不是依赖可能过时的控件显示值。

        Returns:
            float: 当前实际的反馈频率
        """
        try:
            logger.info("🔍 开始实时计算当前反馈频率...")

            # 获取FBMUX的当前选择
            fb_mux = self._get_fbmux_selection()
            logger.info(f"📊 当前FBMUX选择: {fb_mux}")

            # 先检查PLL2Cin控件的值作为参考
            pll2cin_display = self._get_pll2cin_frequency()
            logger.info(f"📊 PLL2Cin控件显示值: {pll2cin_display} MHz")

            calculated_freq = 0.0

            if fb_mux == 0:
                # CLKout6
                calculated_freq = self._calculate_current_clkout_frequency(6)
                logger.info(f"📊 CLKout6计算结果: {calculated_freq} MHz")
            elif fb_mux == 1:
                # CLKout8
                calculated_freq = self._calculate_current_clkout_frequency(8)
                logger.info(f"📊 CLKout8计算结果: {calculated_freq} MHz")
            elif fb_mux == 2:
                # SYSREF Divider - 从同步系统参考窗口获取
                calculated_freq = self._get_current_sysref_frequency()
                logger.info(f"📊 SYSREF计算结果: {calculated_freq} MHz")
            elif fb_mux == 4:
                # External - 外部反馈
                calculated_freq = self._get_external_feedback_frequency()
                logger.info(f"📊 External反馈结果: {calculated_freq} MHz")
            else:
                logger.warning(f"❌ 未知的FBMUX值: {fb_mux}")
                # 如果FBMUX值未知，但PLL2Cin有值，则使用PLL2Cin的值
                if pll2cin_display > 0:
                    logger.info(f"🔄 使用PLL2Cin显示值作为反馈频率: {pll2cin_display} MHz")
                    return pll2cin_display
                return 0.0

            # 比较计算值和显示值
            if calculated_freq > 0 and pll2cin_display > 0:
                if abs(calculated_freq - pll2cin_display) > 0.1:
                    logger.warning(f"⚠️ 计算值({calculated_freq})与PLL2Cin显示值({pll2cin_display})不一致！")
                    logger.info("🔄 使用PLL2Cin显示值作为反馈频率")
                    return pll2cin_display
                else:
                    logger.info(f"✅ 计算值与显示值一致: {calculated_freq} MHz")
                    return calculated_freq
            elif pll2cin_display > 0:
                # 如果计算值为0但显示值有效，使用显示值
                logger.info(f"🔄 计算值无效，使用PLL2Cin显示值: {pll2cin_display} MHz")
                return pll2cin_display
            elif calculated_freq > 0:
                # 如果显示值为0但计算值有效，使用计算值
                logger.info(f"🔄 显示值无效，使用计算值: {calculated_freq} MHz")
                return calculated_freq
            else:
                # 两个值都无效，尝试估算
                logger.warning("⚠️ 计算值和显示值都无效，尝试估算反馈频率")
                estimated_freq = self._estimate_feedback_frequency_for_initialization()
                logger.info(f"📊 估算的反馈频率: {estimated_freq} MHz")
                return estimated_freq

        except Exception as e:
            logger.error(f"实时计算反馈频率时出错: {str(e)}")
            # 出错时尝试使用PLL2Cin显示值
            try:
                pll2cin_display = self._get_pll2cin_frequency()
                if pll2cin_display > 0:
                    logger.info(f"🔄 出错时使用PLL2Cin显示值: {pll2cin_display} MHz")
                    return pll2cin_display
            except Exception:
                pass
            return 0.0

    def _calculate_current_clkout_frequency(self, clkout_number):
        """实时计算指定CLKout的当前频率

        Args:
            clkout_number: CLKout编号 (6 或 8)

        Returns:
            float: CLKout的当前频率
        """
        try:
            # 获取当前VCODistFreq
            vco_dist_freq = self._get_vco_dist_frequency()
            if vco_dist_freq <= 0:
                logger.debug(f"VCODistFreq为0，无法计算CLKout{clkout_number}频率")
                return 0.0

            # 获取对应的分频器值
            divider_value = self._get_current_clkout_divider(clkout_number)
            if divider_value <= 0:
                logger.debug(f"CLKout{clkout_number}分频器为0，无法计算频率")
                return 0.0

            # 计算CLKout频率
            clkout_freq = vco_dist_freq / divider_value
            logger.debug(f"CLKout{clkout_number}频率计算: {vco_dist_freq} / {divider_value} = {clkout_freq}")

            return clkout_freq

        except Exception as e:
            logger.error(f"计算CLKout{clkout_number}频率时出错: {str(e)}")
            return 0.0

    def _get_current_clkout_divider(self, clkout_number):
        """获取指定CLKout的当前分频器值

        Args:
            clkout_number: CLKout编号

        Returns:
            int: 分频器值
        """
        try:
            # 这里需要从时钟输出窗口或寄存器获取分频器值
            # 先尝试从RegisterManager获取
            if self.register_manager:
                if clkout_number == 6:
                    # CLKout6对应DCLK6_7DIV
                    divider = self.register_manager.get_bit_field_value("0x10", "DCLK6_7DIV[9:0]")
                    return max(1, divider) if divider is not None else 10  # 默认值10
                elif clkout_number == 8:
                    # CLKout8对应DCLK8_9DIV
                    divider = self.register_manager.get_bit_field_value("0x11", "DCLK8_9DIV[9:0]")
                    return max(1, divider) if divider is not None else 10  # 默认值10

            # 如果无法从寄存器获取，使用默认值
            logger.debug(f"无法获取CLKout{clkout_number}分频器，使用默认值10")
            return 10

        except Exception as e:
            logger.error(f"获取CLKout{clkout_number}分频器时出错: {str(e)}")
            return 10

    def _get_current_sysref_frequency(self):
        """获取当前SYSREF频率

        Returns:
            float: SYSREF频率
        """
        try:
            # 尝试从缓存获取
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            if hasattr(bus, 'get_cached_sysref_frequency'):
                cached_freq = bus.get_cached_sysref_frequency()
                if cached_freq and cached_freq > 0:
                    logger.debug(f"从缓存获取SYSREF频率: {cached_freq}")
                    return cached_freq

            # 如果缓存中没有，尝试从同步系统参考窗口获取
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'sync_sysref_window'):
                sync_window = main_window.sync_sysref_window
                if sync_window and hasattr(sync_window.ui, 'SyncSysrefFreq1'):
                    freq_text = sync_window.ui.SyncSysrefFreq1.text()
                    try:
                        freq = float(freq_text) if freq_text else 0.0
                        logger.debug(f"从同步系统参考窗口获取SYSREF频率: {freq}")
                        return freq
                    except ValueError:
                        pass

            logger.debug("无法获取SYSREF频率，返回0")
            return 0.0

        except Exception as e:
            logger.error(f"获取SYSREF频率时出错: {str(e)}")
            return 0.0

    def _estimate_feedback_frequency_for_initialization(self):
        """为初始化估算反馈频率

        在Feedback Mux模式下，首次打开时可能无法准确获取反馈频率，
        因为它依赖于VCODistFreq和时钟输出分频器，这些值可能还没有计算出来。
        这里使用一些启发式方法来估算一个合理的反馈频率。

        Returns:
            float: 估算的反馈频率
        """
        try:
            logger.debug("估算Feedback Mux模式的反馈频率...")

            # 方法1: 尝试从缓存或其他窗口获取VCODistFreq
            vco_dist_freq = self._get_vco_dist_frequency()
            if vco_dist_freq > 0:
                logger.debug(f"从VCODistFreq估算: {vco_dist_freq}")

                # 获取FBMUX选择
                fb_mux = self._get_fbmux_selection()

                # 根据FBMUX选择估算分频器
                estimated_divider = self._estimate_feedback_divider(fb_mux)

                if estimated_divider > 0:
                    estimated_feedback_freq = vco_dist_freq / estimated_divider
                    logger.debug(f"估算反馈频率: {vco_dist_freq} / {estimated_divider} = {estimated_feedback_freq}")
                    return estimated_feedback_freq

            # 方法2: 基于典型配置的经验值
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
            if oscin_freq > 0:
                # 假设典型的反馈频率约为OSCin频率的2倍（这是一个经验值）
                estimated_feedback_freq = oscin_freq * 2
                logger.debug(f"基于OSCin频率估算反馈频率: {oscin_freq} × 2 = {estimated_feedback_freq}")
                return estimated_feedback_freq

            # 方法3: 使用固定的典型值
            typical_feedback_freq = 245.76  # MHz，这是一个常见的反馈频率
            logger.debug(f"使用典型反馈频率: {typical_feedback_freq} MHz")
            return typical_feedback_freq

        except Exception as e:
            logger.error(f"估算反馈频率时出错: {str(e)}")
            return 245.76  # 返回一个安全的默认值

    def _get_fbmux_selection(self):
        """获取FBMUX的当前选择

        Returns:
            int: FBMUX选择值
        """
        try:
            if hasattr(self.ui, "FBMUX"):
                if hasattr(self.ui.FBMUX, 'currentData'):
                    return self.ui.FBMUX.currentData() or 0
                else:
                    return self.ui.FBMUX.currentIndex()
            return 0
        except Exception as e:
            logger.error(f"获取FBMUX选择时出错: {str(e)}")
            return 0

    def _estimate_feedback_divider(self, fb_mux):
        """根据FBMUX选择估算反馈分频器

        Args:
            fb_mux: FBMUX选择值

        Returns:
            int: 估算的分频器值
        """
        try:
            # 根据FBMUX选择返回典型的分频器值
            if fb_mux == 0:
                # CLKout6，典型分频器值
                return 10
            elif fb_mux == 1:
                # CLKout8，典型分频器值
                return 10
            elif fb_mux == 2:
                # SYSREF Divider，典型分频器值
                return 1
            else:
                return 10  # 默认值
        except Exception as e:
            logger.error(f"估算反馈分频器时出错: {str(e)}")
            return 10

    def _get_pll2r_value_from_config(self, widget_name, default_value, min_val, max_val):
        """从配置获取PLL2RDivider值"""
        # 避免未使用参数警告
        _ = min_val, max_val

        if not self.widget_register_map or widget_name not in self.widget_register_map:
            return default_value, False

        widget_info = self.widget_register_map[widget_name]

        # 尝试解析默认值
        if "default_value" in widget_info:
            parsed_val, success = self._parse_default_value(widget_info["default_value"])
            if success:
                default_value = parsed_val

        # 尝试从RegisterManager获取值
        if self.register_manager:
            reg_addr = widget_info.get("register_addr")
            bit_def_name = widget_info.get("bit_def_name")
            if reg_addr and bit_def_name:
                value, success = self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
                if success:
                    return int(value), True

        return default_value, False



    def _register_cross_register_controls(self):
        """注册跨寄存器控件（使用新的CrossRegisterWidgetManager）"""
        try:
            # 注册PLL2NDivider为跨寄存器控件
            if hasattr(self.ui, "PLL2NDivider"):
                config = CrossRegisterWidgetConfig(
                    widget_name="PLL2NDivider",
                    bit_fields=["PLL2_N[17:16]", "PLL2_N[15:0]"],
                    reg_addrs=["0x76", "0x77"],
                    default_value="000000000000000000",
                    widget_type="spinbox",
                    options="1:262143",
                    min_value=1,
                    max_value=262143
                )

                if self.cross_register_manager.register_widget(config):
                    logger.info("已注册PLL2NDivider为跨寄存器控件")
                    # 初始化PLL2NDivider控件值
                    if self.cross_register_manager.initialize_widget("PLL2NDivider"):
                        logger.info("PLL2NDivider控件初始化成功")
                    else:
                        logger.error("PLL2NDivider控件初始化失败")
                else:
                    logger.error("注册PLL2NDivider跨寄存器控件失败")

            # 注册PLL1RDividerSetting控件（动态映射控件）
            if hasattr(self.ui, "PLL1RDividerSetting"):
                # PLL1RDividerSetting是一个动态控件，根据当前时钟源映射到不同寄存器
                # 默认映射到ClkIn0对应的寄存器0x63
                success = self._register_cross_register_control(
                    widget_name="PLL1RDividerSetting",
                    bit_fields=["CLKin0_R[13:0]"],
                    reg_addrs=["0x63"],
                    default_value="00000001111000",
                    widget_type="spinbox",
                    options="1:16383"
                )
                if success:
                    logger.info("已注册PLL1RDividerSetting为动态映射控件")
                    # 设置控件范围
                    self.ui.PLL1RDividerSetting.setMinimum(1)
                    self.ui.PLL1RDividerSetting.setMaximum(16383)
                    logger.info("已设置PLL1RDividerSetting控件范围")
                else:
                    logger.error("注册PLL1RDividerSetting控件失败")

            logger.info("跨寄存器控件注册完成")

        except Exception as e:
            logger.error(f"注册跨寄存器控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _register_cross_register_control(self, widget_name, bit_fields, reg_addrs, default_value, widget_type="spinbox", options="0:65535"):
        """注册跨寄存器控件（参考BaseHandler的实现）

        Args:
            widget_name: 控件名称
            bit_fields: 关联的位字段列表，从高位到低位排序
            reg_addrs: 关联的寄存器地址列表，从高位到低位排序
            default_value: 默认值
            widget_type: 控件类型
            options: 选项范围
        """
        try:
            if not hasattr(self.ui, widget_name):
                logger.warning(f"跨寄存器控件 {widget_name} 不存在")
                return False

            # 如果控件已在映射表中，则跳过
            if widget_name in self.widget_register_map:
                logger.info(f"跨寄存器控件 {widget_name} 已在映射表中")
                return True

            # 确保bit_fields和reg_addrs长度一致
            if len(bit_fields) != len(reg_addrs):
                logger.error("跨寄存器控件错误: bit_fields和reg_addrs长度不一致")
                return False

            # 创建映射条目
            self.widget_register_map[widget_name] = {
                "register_addr": reg_addrs[0],  # 使用第一个寄存器地址作为主地址
                "widget_type": widget_type,
                "default_value": default_value,
                "bit_def": {
                    "options": options,
                    "default": default_value,
                    "name": "_".join(bit_fields),  # 组合名称
                    "is_cross_register": True,     # 特殊标记，表示跨寄存器字段
                    "bits": bit_fields,            # 存储相关联的寄存器位字段
                    "registers": reg_addrs         # 存储相关联的寄存器地址
                }
            }

            logger.info(f"已注册跨寄存器控件 {widget_name}, 关联寄存器: {', '.join(reg_addrs)}")
            return True

        except Exception as e:
            logger.error(f"注册跨寄存器控件 {widget_name} 时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False




    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"PLL: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if widget_name == "PLL1PD":
            self._handle_pll1_power_down()
        elif widget_name == "PLL2PD":
            self._handle_pll2_power_down()
        elif widget_name == "PLL1RST":
            self._handle_pll1_reset()
        elif widget_name == "PLL2R3":
            self._handle_pll2r3_change()
        elif widget_name in ["PLL1RDividerSetting", "PLL2RDivider", "PLL2NDivider"]:
            self._handle_divider_change(widget_name)

        # 注意：寄存器表格跳转功能已在基类ModernBaseHandler中处理
        # 不需要在这里重复调用，避免跳转逻辑混乱

    def update_register_value(self, widget_name, value):
        """重写父类方法，处理特殊控件的值更新"""
        try:
            # 处理跨寄存器控件
            if self.cross_register_manager.is_cross_register_widget(widget_name):
                success = self.cross_register_manager.update_registers(widget_name, value)
                if success:
                    logger.info(f"跨寄存器控件 {widget_name} 更新成功")

                    # 获取跨寄存器控件配置，触发每个相关寄存器的标准处理流程
                    config = self.cross_register_manager.get_widget_config(widget_name)
                    if config:
                        for reg_addr in config.reg_addrs:
                            # 将寄存器地址转换为整数（去掉0x前缀）
                            try:
                                reg_addr_int = int(reg_addr, 16)
                                # 获取当前寄存器值
                                current_reg_value = self.register_manager.get_register_value(reg_addr)

                                # 发送RegisterUpdateBus信号
                                try:
                                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                                    bus_instance = RegisterUpdateBus.instance()
                                    if bus_instance:
                                        bus_instance.register_updated.emit(reg_addr, current_reg_value)
                                        logger.debug(f"已发送跨寄存器控件 {widget_name} 的寄存器更新信号: {reg_addr}")
                                except Exception as e:
                                    logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")

                                # 触发寄存器表格跳转（只跳转到第一个寄存器）
                                if reg_addr == config.reg_addrs[0] and not self._is_in_batch_operation():
                                    logger.info(f"跨寄存器控件 {widget_name} 触发寄存器表格跳转到 {reg_addr}")
                                    self._trigger_register_table_navigation(reg_addr_int)

                            except Exception as e:
                                logger.error(f"处理跨寄存器控件 {widget_name} 的寄存器 {reg_addr} 时出错: {str(e)}")

                    # 触发频率重新计算
                    self.calculate_output_frequencies()
                else:
                    logger.error(f"跨寄存器控件 {widget_name} 更新失败")
                return

            # 特殊处理PLL2R3控件
            elif widget_name == "PLL2R3" and hasattr(self.ui, "PLL2R3"):
                # 从ComboBox中获取itemData作为实际寄存器值
                actual_value = self.ui.PLL2R3.itemData(value)
                if actual_value is not None:
                    logger.info(f"更新PLL2R3寄存器值: 索引={value}, 实际值={actual_value}")
                    # 调用父类方法，传递实际值
                    super().update_register_value(widget_name, actual_value)
                else:
                    logger.error(f"PLL2R3控件索引{value}的itemData为None")
            else:
                # 对其他控件使用默认处理
                super().update_register_value(widget_name, value)
        except Exception as e:
            logger.error(f"更新寄存器值时发生错误: {str(e)}")



    def _handle_pll2r3_change(self):
        """处理PLL2R3控件变化"""
        try:
            if hasattr(self.ui, "PLL2R3"):
                index = self.ui.PLL2R3.currentIndex()
                text = self.ui.PLL2R3.currentText()
                value = self.ui.PLL2R3.itemData(index)
                if value is None:
                    # 如果itemData未设置，使用映射表
                    pll2r3_values = {0: 0, 1: 1, 2: 2, 3: 4}
                    value = pll2r3_values.get(index, 0)
                logger.info(f"PLL2R3选择了电阻值: {text}, 索引: {index}, 实际值: {value}")
        except Exception as e:
            logger.error(f"处理PLL2R3变化时出错: {str(e)}")

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"PLL: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 处理跨寄存器控件的反向同步
        if hasattr(self, 'cross_register_manager') and self.cross_register_manager:
            updated_widgets = self.cross_register_manager.update_widgets_from_register(reg_addr)
            if updated_widgets:
                logger.info(f"PLL: 寄存器 {reg_addr} 更新了跨寄存器控件: {updated_widgets}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self, force_mode_update=False):
        """计算PLL输出频率 - 支持模式化计算，默认使用传统方法

        Args:
            force_mode_update: 是否强制更新模式设置
        """
        try:
            # 移除防循环机制，因为不再有自动调整逻辑
            # if getattr(self, '_updating_pll2_n_divider', False):
            #     logger.debug("正在自动更新PLL2NDivider，跳过频率计算以防止循环")
            #     return

            # 在计算前进行诊断
            logger.info("开始PLL频率计算...")
            self._diagnose_pfd_calculation_inputs()

            # 检查是否启用了模式化计算
            if self._is_mode_based_calculation_enabled():
                # 使用模式化计算
                self._calculate_with_mode_detection(force_mode_update)
            else:
                # 使用传统的计算方法（默认DUAL MODE行为）
                self._calculate_with_traditional_method()

            # 计算完成后检查PLL锁存状态
            self._check_and_emit_pll_lock_status()

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _check_and_emit_pll_lock_status(self):
        """检查PLL锁存状态并发送信号"""
        try:
            # 检查PLL1锁存状态
            pll1_locked, pll1_r_output, pll1_n_output = self._calculate_pll1_lock_status()
            self.pll1_lock_status_changed.emit(pll1_locked, pll1_r_output, pll1_n_output)

            # 检查PLL2锁存状态
            pll2_locked, pll2_r_output, pll2_n_output = self._calculate_pll2_lock_status()
            self.pll2_lock_status_changed.emit(pll2_locked, pll2_r_output, pll2_n_output)

            logger.debug(f"PLL锁存状态: PLL1={'锁存' if pll1_locked else '未锁存'}, PLL2={'锁存' if pll2_locked else '未锁存'}")

        except Exception as e:
            logger.debug(f"检查PLL锁存状态失败: {str(e)}")

    def _calculate_pll1_lock_status(self):
        """计算PLL1锁存状态

        PLL1锁存条件: R DIVIDER的输入除以R DIVIDER的值 = N DIVIDER的输入除以N DIVIDER的值

        根据PLL1的架构，R DIVIDER的输入来自当前选择的时钟源（通常是OSCin）
        N DIVIDER的输入根据PLL1 NCLK MUX的选择确定

        Returns:
            tuple: (is_locked, r_divider_output, n_divider_output)
        """
        try:
            # 获取R DIVIDER部分: 当前时钟源频率 / PLL1RDividerSetting
            # R DIVIDER的输入通常来自OSCin（当前选择的时钟源）
            r_divider_input_freq = 0.0
            if hasattr(self.ui, "OSCinFreq"):
                try:
                    r_divider_input_freq = float(self.ui.OSCinFreq.text())
                except Exception:
                    r_divider_input_freq = 0.0

            # 如果OSCin为0，尝试使用FreFin作为备选
            if r_divider_input_freq == 0.0 and hasattr(self.ui, "FreFin"):
                try:
                    r_divider_input_freq = float(self.ui.FreFin.text())
                except Exception:
                    r_divider_input_freq = 0.0

            # 从寄存器读取实际的PLL1 R Divider值，而不是UI控件值
            pll1_r_divider = self._get_actual_pll1_r_divider_from_register()

            r_divider_output = r_divider_input_freq / pll1_r_divider if pll1_r_divider > 0 else 0.0

            # 获取N DIVIDER部分: N_DIVIDER_INPUT / PLL1NDivider
            pll1_n_divider = 1
            if hasattr(self.ui, "PLL1NDivider"):
                pll1_n_divider = max(1, self.ui.PLL1NDivider.value())

            # 获取N DIVIDER的输入频率（根据PLL1 NCLK MUX选择）
            n_divider_input_freq = self._get_pll1_n_divider_input_frequency()
            n_divider_output = n_divider_input_freq / pll1_n_divider if pll1_n_divider > 0 else 0.0

            # 添加详细的调试信息
            logger.debug("PLL1锁存状态计算:")
            logger.debug(f"  R DIVIDER输入频率: {r_divider_input_freq:.6f} MHz")
            logger.debug(f"  R DIVIDER值: {pll1_r_divider}")
            logger.debug(f"  R DIVIDER输出: {r_divider_output:.6f} MHz")
            logger.debug(f"  N DIVIDER输入频率: {n_divider_input_freq:.6f} MHz")
            logger.debug(f"  N DIVIDER值: {pll1_n_divider}")
            logger.debug(f"  N DIVIDER输出: {n_divider_output:.6f} MHz")

            # 判断是否锁存（允许0.1%的误差）
            if r_divider_output > 0 and n_divider_output > 0:
                relative_error = abs(r_divider_output - n_divider_output) / max(r_divider_output, n_divider_output)
                is_locked = relative_error < 0.001  # 0.1%误差内认为锁存

                logger.debug(f"  相对误差: {relative_error:.3%}")
                logger.debug(f"  锁存状态: {'已锁存' if is_locked else '未锁存'}")

                return is_locked, r_divider_output, n_divider_output
            else:
                logger.debug(f"  ❌ 输入参数无效: R输出={r_divider_output}, N输出={n_divider_output}")
                return False, 0.0, 0.0

        except Exception as e:
            logger.debug(f"计算PLL1锁存状态失败: {str(e)}")
            return False, 0.0, 0.0

    def _get_actual_pll1_r_divider_from_register(self):
        """从寄存器读取实际的PLL1 R Divider值

        PLL1 R Divider根据当前时钟源映射到不同寄存器:
        - CLKin0: 0x63.CLKin0_R[13:0]
        - CLKin1: 0x65.CLKin1_R[13:0]
        - CLKin2: 0x67.CLKin2_R[13:0]

        Returns:
            int: PLL1 R Divider的实际值
        """
        try:
            if not self.register_manager:
                logger.debug("RegisterManager不可用，使用UI控件值作为备选")
                return max(1, self.ui.PLL1RDividerSetting.value()) if hasattr(self.ui, "PLL1RDividerSetting") else 120

            # 尝试从不同的时钟源寄存器获取PLL1 R Divider值
            clkin_registers = [
                ("0x63", "CLKin0_R[13:0]"),  # CLKin0
                ("0x65", "CLKin1_R[13:0]"),  # CLKin1
                ("0x67", "CLKin2_R[13:0]"),  # CLKin2
            ]

            for reg_addr, bit_field in clkin_registers:
                try:
                    value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
                    if value > 0:  # 找到非零值，使用它
                        logger.debug(f"从寄存器 {reg_addr}.{bit_field} 读取PLL1 R Divider: {value}")
                        return max(1, value)
                except Exception as e:
                    logger.debug(f"读取寄存器 {reg_addr}.{bit_field} 失败: {str(e)}")
                    continue

            # 如果所有寄存器都读取失败，使用UI控件值作为备选
            logger.debug("所有寄存器读取失败，使用UI控件值作为备选")
            if hasattr(self.ui, "PLL1RDividerSetting"):
                return max(1, self.ui.PLL1RDividerSetting.value())
            else:
                return 120  # 默认值

        except Exception as e:
            logger.debug(f"获取PLL1 R Divider值失败: {str(e)}")
            return 120  # 默认值

    def _get_pll1_n_divider_input_frequency(self):
        """获取PLL1 N DIVIDER的输入频率

        根据PLL1 NCLK MUX的选择确定输入来源:
        - 0: OSCin
        - 1: Feedback Mux
        - 2: PLL2 Prescaler

        Returns:
            float: N DIVIDER的输入频率
        """
        try:
            # 获取PLL1 NCLK MUX值
            pll1_nclk_mux = 0
            if hasattr(self.ui, "PLL1NclkMux"):
                pll1_nclk_mux = self.ui.PLL1NclkMux.currentIndex()

            if pll1_nclk_mux == 0:
                # OSCin模式
                if hasattr(self.ui, "OSCinFreq"):
                    try:
                        return float(self.ui.OSCinFreq.text())
                    except Exception:
                        return 122.88  # 默认值
                return 122.88

            elif pll1_nclk_mux == 1:
                # Feedback Mux模式 - 需要从时钟输出计算反馈频率
                return self._get_feedback_mux_frequency()

            elif pll1_nclk_mux == 2:
                # PLL2 Prescaler模式 - 使用VCODist / PLL2Prescaler
                vco_dist_freq = 0.0
                if hasattr(self.ui, "VCODistFreq"):
                    try:
                        vco_dist_freq = float(self.ui.VCODistFreq.text())
                    except Exception:
                        vco_dist_freq = 0.0

                pll2_prescaler = 2  # 默认值
                if hasattr(self.ui, "PLL2Prescaler"):
                    pll2_prescaler = max(1, self.ui.PLL2Prescaler.currentIndex() + 1)

                return vco_dist_freq / pll2_prescaler if pll2_prescaler > 0 else 0.0
            else:
                logger.debug(f"未知的PLL1 NCLK MUX值: {pll1_nclk_mux}")
                return 0.0

        except Exception as e:
            logger.debug(f"获取PLL1 N DIVIDER输入频率失败: {str(e)}")
            return 0.0

    def _calculate_pll2_lock_status(self):
        """计算PLL2锁存状态

        PLL2锁存条件: R DIVIDER的输入除以R DIVIDER的值 = N DIVIDER的输入 × N DIVIDER的值
        根据PLL2RclkMux的选择:
        - PLL2RclkMux = 0 (OSCin): OSCin × Doubler / PLL2RDivider = PLL2NDivider输入 × PLL2NDivider
        - PLL2RclkMux = 1 (PLL1 CLKin): FreFin / PLL2RDivider = PLL2NDivider输入 × PLL2NDivider

        Returns:
            tuple: (is_locked, r_divider_output, n_divider_output)
        """
        try:
            # 获取PLL2RclkMux值
            pll2_rclk_mux = 0
            if hasattr(self.ui, "PLL2RclkMux"):
                pll2_rclk_mux = self.ui.PLL2RclkMux.currentIndex()

            # 计算R DIVIDER部分
            r_divider_output = 0.0

            if pll2_rclk_mux == 0:
                # OSCin模式: OSCin × Doubler / PLL2RDivider
                oscin_freq = 122.88  # 默认值
                if hasattr(self.ui, "OSCinFreq"):
                    try:
                        oscin_freq = float(self.ui.OSCinFreq.text())
                    except Exception:
                        oscin_freq = 122.88

                doubler_value = 1
                if hasattr(self.ui, "Doubler"):
                    doubler_value = self.ui.Doubler.currentIndex() + 1  # 0->1, 1->2

                pll2_r_divider = 1
                if hasattr(self.ui, "PLL2RDivider"):
                    pll2_r_divider = max(1, self.ui.PLL2RDivider.value())

                r_divider_output = oscin_freq * doubler_value / pll2_r_divider

            elif pll2_rclk_mux == 1:
                # PLL1 CLKin模式: FreFin / PLL2RDivider
                fre_fin_freq = 0.0
                if hasattr(self.ui, "FreFin"):
                    try:
                        fre_fin_freq = float(self.ui.FreFin.text())
                    except Exception:
                        fre_fin_freq = 0.0

                pll2_r_divider = 1
                if hasattr(self.ui, "PLL2RDivider"):
                    pll2_r_divider = max(1, self.ui.PLL2RDivider.value())

                r_divider_output = fre_fin_freq / pll2_r_divider

            # 计算N DIVIDER部分: 在锁存状态下，N DIVIDER的输出应该等于R DIVIDER的输出
            # 正确的PLL锁存逻辑：PFD比较参考输入和反馈输入的频率
            # 参考输入 = R DIVIDER输出 = r_divider_output
            # 反馈输入 = N DIVIDER输出 = VCO输出 / N分频器
            # 锁存条件：参考输入 ≈ 反馈输入

            # 获取当前PLL2PFD频率控件的值（这是实际的PFD频率）
            n_divider_output = 0.0
            if hasattr(self.ui, "PLL2PFDFreq"):
                try:
                    n_divider_output = float(self.ui.PLL2PFDFreq.text())
                except (ValueError, AttributeError):
                    n_divider_output = r_divider_output  # 如果无法获取，假设锁存

            # 判断是否锁存（允许0.1%的误差）
            if r_divider_output > 0 and n_divider_output > 0:
                relative_error = abs(r_divider_output - n_divider_output) / max(r_divider_output, n_divider_output)
                is_locked = relative_error < 0.001  # 0.1%误差内认为锁存

                logger.debug("PLL2锁存状态计算:")
                logger.debug(f"  R DIVIDER输出: {r_divider_output:.6f} MHz")
                logger.debug(f"  N DIVIDER输出(PFD频率): {n_divider_output:.6f} MHz")
                logger.debug(f"  相对误差: {relative_error:.3%}")
                logger.debug(f"  锁存状态: {'已锁存' if is_locked else '未锁存'}")

                return is_locked, r_divider_output, n_divider_output
            else:
                logger.debug("PLL2锁存状态计算: 输入参数无效")
                return False, 0.0, 0.0

        except Exception as e:
            logger.debug(f"计算PLL2锁存状态失败: {str(e)}")
            return False, 0.0, 0.0

    def _get_pll2_n_divider_total_value(self):
        """获取PLL2 N DIVIDER的总值（组合高位和低位）"""
        try:
            n_div_high = 0
            n_div_low = 0

            if hasattr(self.ui, 'PLL2NDivider_high'):
                n_div_high = self.ui.PLL2NDivider_high.value()
            if hasattr(self.ui, 'PLL2NDivider_low'):
                n_div_low = self.ui.PLL2NDivider_low.value()

            # 组合: high[1:0] << 16 + low[15:0]
            total_n_divider = (n_div_high << 16) + n_div_low
            return max(1, total_n_divider)

        except Exception as e:
            logger.debug(f"获取PLL2 N DIVIDER总值失败: {str(e)}")
            return 1

    def _get_pll2_n_divider_input_frequency_for_lock(self):
        """获取PLL2 N DIVIDER的输入频率（用于锁存状态计算）"""
        try:
            # 获取PLL2NclkMux值
            pll2_nclk_mux = 0
            if hasattr(self.ui, "PLL2NclkMux"):
                pll2_nclk_mux = self.ui.PLL2NclkMux.currentIndex()

            if pll2_nclk_mux == 0:
                # PLL2 Prescaler模式 - 使用VCODist / PLL2Prescaler
                vco_dist_freq = 0.0
                if hasattr(self.ui, "VCODistFreq"):
                    try:
                        vco_dist_freq = float(self.ui.VCODistFreq.text())
                    except Exception:
                        vco_dist_freq = 0.0

                pll2_prescaler = 2  # 默认值
                if hasattr(self.ui, "PLL2Prescaler"):
                    pll2_prescaler = max(1, self.ui.PLL2Prescaler.currentIndex() + 1)

                return vco_dist_freq / pll2_prescaler if pll2_prescaler > 0 else 0.0

            elif pll2_nclk_mux == 1:
                # Feedback Mux模式 - 从时钟输出计算
                return self._get_feedback_mux_frequency()
            else:
                logger.debug(f"未知的PLL2 NCLK MUX值: {pll2_nclk_mux}")
                return 0.0

        except Exception as e:
            logger.debug(f"获取PLL2 N DIVIDER输入频率失败: {str(e)}")
            return 0.0

    def _is_mode_based_calculation_enabled(self):
        """检查是否启用了模式化计算

        Returns:
            bool: 如果有明确的模式设置或者模式缓存，则启用模式化计算
        """
        try:
            # 如果有模式缓存，说明已经进行过模式设置
            if hasattr(self, '_current_mode_cache') and self._current_mode_cache:
                logger.debug(f"检测到模式缓存: {self._current_mode_cache}，启用模式化计算")
                return True

            # 检查是否有ModernSetModesHandler实例存在且已经设置过模式
            modes_handler = self._get_modes_handler()
            if modes_handler and hasattr(modes_handler, '_last_set_mode'):
                logger.debug("检测到ModernSetModesHandler已设置模式，启用模式化计算")
                return True

            # 默认不启用模式化计算
            logger.debug("未检测到明确的模式设置，使用传统计算方法")
            return False

        except Exception as e:
            logger.error(f"检查模式化计算状态时出错: {str(e)}")
            return False

    def _calculate_with_mode_detection(self, force_mode_update=False):
        """使用模式检测进行计算 - 基于时钟源选择"""
        try:
            # 获取当前模式
            current_mode = self._get_current_mode()
            logger.debug(f"模式化计算 - 当前模式: {current_mode}")

            # 如果需要强制更新模式，则先确保模式设置正确
            if force_mode_update:
                self._ensure_mode_is_set(current_mode)

            # 使用基于时钟源选择的计算方法
            self._calculate_with_clock_source_selection()

            # 更新PLL2Cin控件（如果可见）
            if hasattr(self.ui, "PLL2Cin") and self.ui.PLL2Cin.isVisible():
                self._update_pll2cin_value()

            logger.debug(f"模式化计算完成: 模式={current_mode}")

        except Exception as e:
            logger.error(f"模式化计算时发生错误: {str(e)}")
            # 出错时回退到传统方法
            self._calculate_with_traditional_method()

    def _calculate_with_clock_source_selection(self):
        """基于时钟源选择进行计算 - 核心计算逻辑"""
        try:
            logger.debug("使用基于时钟源选择的计算方法")

            # 1. 获取PLL1的时钟源
            pll1_clock_source, pll1_freq = self._get_pll1_clock_source()
            logger.debug(f"PLL1时钟源: {pll1_clock_source}, 频率: {pll1_freq} MHz")

            # 2. 计算PLL1输出
            pll1_pfd_freq = self._calculate_pll1_output_with_source(pll1_freq)

            # 3. 获取PLL2的时钟源
            pll2_clock_source, pll2_freq = self._get_pll2_clock_source(pll1_pfd_freq)
            logger.debug(f"PLL2时钟源: {pll2_clock_source}, 频率: {pll2_freq} MHz")

            # 4. 计算PLL2输出
            pll2_pfd_freq = self._calculate_pll2_output_with_source(pll2_freq)

            # 5. 计算其他输出频率
            self._calculate_fin0_output(pll2_pfd_freq)
            self._calculate_vco_dist_freq(pll2_pfd_freq)

            logger.debug("基于时钟源选择的计算完成")

        except Exception as e:
            logger.error(f"基于时钟源选择计算时出错: {str(e)}")
            raise

    def _get_pll1_clock_source(self):
        """获取PLL1的时钟源和频率

        Returns:
            tuple: (时钟源名称, 频率值)
        """
        try:
            # 从UI控件获取PLL1_NCLK_MUX的当前值
            pll1_nclk_mux = 0
            if hasattr(self.ui, "PLL1NclkMux"):
                # 从ComboBox控件获取当前选择的值
                if hasattr(self.ui.PLL1NclkMux, 'currentData'):
                    # 如果ComboBox有itemData，使用itemData
                    pll1_nclk_mux = self.ui.PLL1NclkMux.currentData() or 0
                else:
                    # 否则使用currentIndex
                    pll1_nclk_mux = self.ui.PLL1NclkMux.currentIndex()
                logger.debug(f"从UI控件获取PLL1NclkMux值: {pll1_nclk_mux}")
            else:
                logger.warning("PLL1NclkMux控件不存在，使用默认值0")

            if pll1_nclk_mux == 0:
                # 0: OSCin
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
                return "OSCin", oscin_freq
            elif pll1_nclk_mux == 1:
                # 1: Feedback Mux
                feedback_freq = self._get_feedback_mux_frequency()
                return "Feedback Mux", feedback_freq
            elif pll1_nclk_mux == 2:
                # 2: PLL2 Prescaler
                pll2_prescaler_freq = self._get_pll2_prescaler_frequency()
                return "PLL2 Prescaler", pll2_prescaler_freq
            else:
                logger.warning(f"未知的PLL1_NCLK_MUX值: {pll1_nclk_mux}")
                # 默认使用OSCin
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
                return "OSCin", oscin_freq

        except Exception as e:
            logger.error(f"获取PLL1时钟源时出错: {str(e)}")
            # 出错时使用默认OSCin
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
            return "OSCin", oscin_freq

    def _get_pll2_clock_source(self, pll1_pfd_freq):
        """获取PLL2的时钟源和频率

        Args:
            pll1_pfd_freq: PLL1的PFD频率

        Returns:
            tuple: (时钟源名称, 频率值)
        """
        try:
            # 从UI控件获取PLL2_RCLK_MUX的当前值
            pll2_rclk_mux = 0
            if hasattr(self.ui, "PLL2RclkMux"):
                # 从ComboBox控件获取当前选择的值
                if hasattr(self.ui.PLL2RclkMux, 'currentData'):
                    # 如果ComboBox有itemData，使用itemData
                    pll2_rclk_mux = self.ui.PLL2RclkMux.currentData() or 0
                else:
                    # 否则使用currentIndex
                    pll2_rclk_mux = self.ui.PLL2RclkMux.currentIndex()
                logger.debug(f"从UI控件获取PLL2RclkMux值: {pll2_rclk_mux}")
            else:
                logger.warning("PLL2RclkMux控件不存在，使用默认值0")

            if pll2_rclk_mux == 0:
                # 0: OSCin
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
                return "OSCin", oscin_freq
            elif pll2_rclk_mux == 1:
                # 1: PLL1 CLKin (使用FreFin的值)
                fre_fin_freq = 0.0
                if hasattr(self.ui, "FreFin"):
                    fre_fin_text = self.ui.FreFin.text()
                    try:
                        fre_fin_freq = float(fre_fin_text) if fre_fin_text else 0.0
                    except ValueError:
                        logger.warning(f"FreFin值无效: {fre_fin_text}")
                        fre_fin_freq = 0.0
                logger.debug(f"PLL2时钟源选择PLL1 CLKin，使用FreFin频率: {fre_fin_freq} MHz")
                return "PLL1 CLKin", fre_fin_freq
            else:
                logger.warning(f"未知的PLL2_RCLK_MUX值: {pll2_rclk_mux}")
                # 默认使用OSCin
                oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
                return "OSCin", oscin_freq

        except Exception as e:
            logger.error(f"获取PLL2时钟源时出错: {str(e)}")
            # 出错时使用默认OSCin
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0
            return "OSCin", oscin_freq

    def _get_feedback_mux_frequency(self):
        """获取反馈多路复用器的频率"""
        try:
            # 从UI控件获取FB_MUX的当前值
            fb_mux = 0
            if hasattr(self.ui, "FBMUX"):
                # 从ComboBox控件获取当前选择的值
                if hasattr(self.ui.FBMUX, 'currentData'):
                    # 如果ComboBox有itemData，使用itemData
                    fb_mux = self.ui.FBMUX.currentData() or 0
                else:
                    # 否则使用currentIndex
                    fb_mux = self.ui.FBMUX.currentIndex()
                logger.debug(f"从UI控件获取FBMUX值: {fb_mux}")
            else:
                logger.warning("FBMUX控件不存在，使用默认值0")

            # 根据FB_MUX的值获取对应的时钟输出频率
            if fb_mux == 0:
                # CLKout6
                return self._get_clkout_frequency(6)
            elif fb_mux == 1:
                # CLKout8
                return self._get_clkout_frequency(8)
            elif fb_mux == 2:
                # SYSREF Divider
                return self._get_sysref_divider_frequency()
            elif fb_mux == 4:
                # External
                return self._get_external_feedback_frequency()
            else:
                logger.warning(f"未知的FB_MUX值: {fb_mux}")
                return 0.0

        except Exception as e:
            logger.error(f"获取反馈多路复用器频率时出错: {str(e)}")
            return 0.0

    def _get_pll2_prescaler_frequency(self):
        """获取PLL2预分频器的频率

        PLL2 Prescaler频率 = PLL2_PFD_Freq × PLL2NDivider × PLL2Prescaler

        Returns:
            float: PLL2预分频器输出频率
        """
        try:
            # 获取PLL2 PFD频率
            pll2_pfd_freq = 0.0
            if hasattr(self.ui, "PLL2PFDFreq"):
                pll2_pfd_text = self.ui.PLL2PFDFreq.text()
                try:
                    pll2_pfd_freq = float(pll2_pfd_text) if pll2_pfd_text else 0.0
                except ValueError:
                    pll2_pfd_freq = 0.0

            if pll2_pfd_freq <= 0:
                logger.debug("PLL2 PFD频率为0，PLL2预分频器频率为0")
                return 0.0

            # 获取PLL2NDivider值
            pll2_n_divider = 1
            if hasattr(self.ui, "PLL2NDivider"):
                pll2_n_divider = max(1, self.ui.PLL2NDivider.value())

            # 获取PLL2Prescaler值
            pll2_prescaler = self._get_pll2_prescaler_value()

            # 计算PLL2预分频器频率
            prescaler_freq = pll2_pfd_freq * pll2_n_divider * pll2_prescaler

            logger.debug(f"PLL2预分频器频率计算: {pll2_pfd_freq} × {pll2_n_divider} × {pll2_prescaler} = {prescaler_freq:.6f} MHz")

            return prescaler_freq

        except Exception as e:
            logger.error(f"获取PLL2预分频器频率时出错: {str(e)}")
            return 0.0

    def _get_current_selected_clkin_frequency(self):
        """获取当前选择的CLKin频率"""
        try:
            # 从RegisterUpdateBus获取当前选择的时钟源频率
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            current_source = bus.get_current_clock_source()
            if current_source:
                freq = bus.get_clock_frequency(current_source)
                logger.debug(f"当前选择的CLKin频率: {current_source} = {freq} MHz")
                return freq
            else:
                # 如果没有选择的时钟源，使用FreFin的值
                fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
                logger.debug(f"使用FreFin作为当前CLKin频率: {fin_freq} MHz")
                return fin_freq

        except Exception as e:
            logger.error(f"获取当前选择的CLKin频率时出错: {str(e)}")
            # 出错时使用FreFin的值
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
            return fin_freq

    def _get_clkout_frequency(self, clkout_number):
        """获取指定CLKout的频率

        Args:
            clkout_number: CLKout编号 (如6表示CLKout6)

        Returns:
            float: CLKout频率值
        """
        try:
            if clkout_number == 6:
                # CLKout6频率 = VCODistFreq / DCLK6_7DIV
                return self._get_clkout6_frequency()
            elif clkout_number == 8:
                # CLKout8频率 = VCODistFreq / DCLK8_9DIV
                return self._get_clkout8_frequency()
            else:
                logger.warning(f"暂不支持CLKout{clkout_number}频率计算")
                return 0.0

        except Exception as e:
            logger.error(f"获取CLKout{clkout_number}频率时出错: {str(e)}")
            return 0.0

    def _get_clkout6_frequency(self):
        """获取CLKout6的频率

        计算公式: CLKout6频率 = VCODistFreq / DCLK6_7DIV

        Returns:
            float: CLKout6频率值
        """
        try:
            # 1. 获取VCODistFreq的值
            vco_dist_freq = 0.0
            if hasattr(self.ui, "VCODistFreq"):
                vco_dist_freq = self._get_float_from_lineedit(self.ui.VCODistFreq)
                logger.debug(f"VCODistFreq值: {vco_dist_freq} MHz")
            else:
                logger.warning("VCODistFreq控件不存在")
                return 0.0

            # 2. 获取DCLK6_7DIV的值
            dclk6_7div = self._get_dclk6_7div_value()
            logger.debug(f"DCLK6_7DIV值: {dclk6_7div}")

            # 3. 计算CLKout6频率
            if dclk6_7div > 0:
                clkout6_freq = vco_dist_freq / dclk6_7div
                logger.debug(f"CLKout6频率计算: {vco_dist_freq} / {dclk6_7div} = {clkout6_freq} MHz")
                return clkout6_freq
            else:
                logger.warning("DCLK6_7DIV值为0，无法计算CLKout6频率")
                return 0.0

        except Exception as e:
            logger.error(f"获取CLKout6频率时出错: {str(e)}")
            return 0.0

    def _get_dclk6_7div_value(self):
        """从ModernClkOutputsHandler获取DCLK6_7DIV控件的值

        Returns:
            int: DCLK6_7DIV的分频比值
        """
        try:
            logger.debug("🔍 开始获取DCLK6_7DIV值...")

            # 尝试从主窗口获取时钟输出窗口实例
            main_window = self._get_main_window()
            logger.debug(f"🏠 主窗口: {main_window}")

            if main_window:
                # 调试：列出主窗口的所有属性，查找时钟输出窗口
                window_attrs = [attr for attr in dir(main_window) if 'window' in attr.lower() and not attr.startswith('_')]
                logger.debug(f"🔍 主窗口中包含'window'的属性: {window_attrs}")

                clk_outputs_handler = None

                # 方法1: 通过clk_output_window属性访问（传统方式）
                if hasattr(main_window, 'clk_output_window') and main_window.clk_output_window:
                    clk_outputs_handler = main_window.clk_output_window
                    logger.debug(f"✅ 通过clk_output_window属性找到时钟输出处理器: {clk_outputs_handler}")

                # 方法1.5: 通过插件系统访问（现代方式）
                elif hasattr(main_window, 'plugin_integration_service'):
                    plugin_service = main_window.plugin_integration_service
                    if hasattr(plugin_service, 'get_plugin_window'):
                        clk_outputs_handler = plugin_service.get_plugin_window("时钟输出")
                        if clk_outputs_handler:
                            logger.debug(f"✅ 通过插件系统找到时钟输出处理器: {clk_outputs_handler}")

                # 方法1.6: 通过查找子窗口的方式（备用方法）
                if not clk_outputs_handler:
                    for child in main_window.findChildren(QtCore.QObject):
                        if hasattr(child, 'ui') and hasattr(child.ui, 'DCLK6_7DIV'):
                            clk_outputs_handler = child
                            logger.debug(f"✅ 通过子窗口查找找到时钟输出处理器: {clk_outputs_handler}")
                            break

                if clk_outputs_handler and hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                    dclk6_7div_value = clk_outputs_handler.ui.DCLK6_7DIV.value()
                    logger.info(f"✅ 从时钟输出窗口获取DCLK6_7DIV值: {dclk6_7div_value}")
                    return dclk6_7div_value
                elif clk_outputs_handler:
                    logger.warning("❌ DCLK6_7DIV控件不存在")
                    # 列出可用的控件
                    if hasattr(clk_outputs_handler, 'ui'):
                        available_attrs = [attr for attr in dir(clk_outputs_handler.ui) if not attr.startswith('_')]
                        logger.debug(f"📋 可用控件: {available_attrs[:10]}...")  # 只显示前10个
                else:
                    # 使用防重复警告机制
                    self._log_clk_output_window_warning()

                # 方法2: 尝试其他可能的属性名
                alternative_attrs = ['clk_outputs_window', 'clock_output_window', 'clkoutput_window', 'output_window']
                for attr_name in alternative_attrs:
                    if hasattr(main_window, attr_name):
                        window = getattr(main_window, attr_name)
                        logger.debug(f"🔍 找到属性 {attr_name}: {window}")
                        if window and hasattr(window, 'ui') and hasattr(window.ui, 'DCLK6_7DIV'):
                            dclk6_7div_value = window.ui.DCLK6_7DIV.value()
                            logger.info(f"✅ 从{attr_name}获取DCLK6_7DIV值: {dclk6_7div_value}")
                            return dclk6_7div_value
                        elif window:
                            logger.debug(f"📋 {attr_name}存在但没有DCLK6_7DIV控件")

                # 方法3: 遍历所有可能的窗口属性
                for attr_name in window_attrs:
                    if hasattr(main_window, attr_name):
                        window = getattr(main_window, attr_name)
                        if window and hasattr(window, 'ui') and hasattr(window.ui, 'DCLK6_7DIV'):
                            dclk6_7div_value = window.ui.DCLK6_7DIV.value()
                            logger.info(f"✅ 从{attr_name}获取DCLK6_7DIV值: {dclk6_7div_value}")
                            return dclk6_7div_value
            else:
                logger.warning("❌ 主窗口不存在")

            # 方法4: 尝试通过事件总线获取缓存的值
            logger.debug("🔄 尝试通过事件总线获取DCLK6_7DIV值...")
            try:
                from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                bus = RegisterUpdateBus.instance()

                # 检查是否有缓存的DCLK6_7DIV值
                if hasattr(bus, '_clock_output_dividers'):
                    dclk6_7div_value = bus._clock_output_dividers.get('DCLK6_7DIV', None)
                    if dclk6_7div_value is not None:
                        logger.info(f"✅ 从事件总线获取DCLK6_7DIV值: {dclk6_7div_value}")
                        return dclk6_7div_value

            except Exception as e:
                logger.debug(f"从事件总线获取DCLK6_7DIV值失败: {str(e)}")

            # 方法5: 尝试从寄存器获取
            logger.debug("🔄 尝试从寄存器获取DCLK6_7DIV值...")
            if self.register_manager:
                # 尝试不同的寄存器地址和位字段名称
                possible_locations = [
                    ("0x65", "DCLK6_7_DIV[9:0]"),
                    ("0x28", "DCLK6_7_DIV[9:0]"),  # 根据日志，可能在0x28
                    ("0x65", "DCLK6_7DIV[9:0]"),
                    ("0x28", "DCLK6_7DIV[9:0]")
                ]

                for reg_addr, bit_field in possible_locations:
                    try:
                        dclk6_7div_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
                        if dclk6_7div_value is not None and dclk6_7div_value > 0:
                            logger.info(f"✅ 从寄存器{reg_addr}[{bit_field}]获取DCLK6_7DIV值: {dclk6_7div_value}")
                            return dclk6_7div_value
                    except Exception as e:
                        logger.debug(f"从寄存器{reg_addr}[{bit_field}]获取失败: {str(e)}")

                logger.warning("❌ 从所有可能的寄存器位置获取DCLK6_7DIV值都失败")
            else:
                logger.warning("❌ 寄存器管理器不存在")

            # 如果都无法获取，使用默认值
            logger.warning("⚠️ 无法获取DCLK6_7DIV值，使用默认值8")
            return 8

        except Exception as e:
            logger.error(f"获取DCLK6_7DIV值时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 8  # 返回默认值

    def _get_clkout8_frequency(self):
        """获取CLKout8的频率

        计算公式: CLKout8频率 = VCODistFreq / DCLK8_9DIV

        Returns:
            float: CLKout8频率值
        """
        try:
            # 1. 获取VCODistFreq的值
            vco_dist_freq = 0.0
            if hasattr(self.ui, "VCODistFreq"):
                vco_dist_freq = self._get_float_from_lineedit(self.ui.VCODistFreq)
                logger.debug(f"VCODistFreq值: {vco_dist_freq} MHz")
            else:
                logger.warning("VCODistFreq控件不存在")
                return 0.0

            # 2. 获取DCLK8_9DIV的值
            dclk8_9div = self._get_dclk8_9div_value()
            logger.debug(f"DCLK8_9DIV值: {dclk8_9div}")

            # 3. 计算CLKout8频率
            if dclk8_9div > 0:
                clkout8_freq = vco_dist_freq / dclk8_9div
                logger.debug(f"CLKout8频率计算: {vco_dist_freq} / {dclk8_9div} = {clkout8_freq} MHz")
                return clkout8_freq
            else:
                logger.warning("DCLK8_9DIV值为0，无法计算CLKout8频率")
                return 0.0

        except Exception as e:
            logger.error(f"获取CLKout8频率时出错: {str(e)}")
            return 0.0

    def _get_sysref_frequency(self):
        """获取SYSREF频率（优先从缓存获取，然后从同步系统参考窗口）

        Returns:
            float: SYSREF频率值
        """
        try:
            logger.info("【SYSREF频率获取】开始获取SYSREF频率...")

            # 首先尝试从事件总线缓存获取（SyncSysrefFreq1的缓存值）
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            logger.info(f"【SYSREF频率获取】RegisterUpdateBus实例: {bus is not None}")

            if bus and hasattr(bus, 'get_cached_sysref_freq'):
                cached_freq = bus.get_cached_sysref_freq()
                logger.info(f"【SYSREF频率获取】缓存中的SYSREF频率(来自SyncSysrefFreq1): {cached_freq}")
                if cached_freq is not None:
                    logger.info(f"【SYSREF频率获取】✅ 从事件总线缓存获取SYSREF频率: {cached_freq:.5f} MHz")
                    return cached_freq
                else:
                    logger.info("【SYSREF频率获取】缓存中没有SYSREF频率值")

                    # 尝试主动计算SYSREF频率并缓存
                    calculated_freq = self._calculate_sysref_frequency_from_pll2pfd()
                    if calculated_freq > 0:
                        logger.info(f"【SYSREF频率获取】✅ 主动计算得到SYSREF频率: {calculated_freq:.5f} MHz")

                        # 防止循环：只有在不是从SYSREF信号更新时才缓存
                        if not getattr(self, '_updating_from_sysref_signal', False):
                            bus.cache_sysref_freq(calculated_freq)
                            logger.debug("已缓存主动计算的SYSREF频率")
                        else:
                            logger.debug("正在处理SYSREF信号更新，跳过缓存以避免循环")

                        return calculated_freq
            else:
                logger.warning("【SYSREF频率获取】RegisterUpdateBus没有get_cached_sysref_freq方法")

            # 如果缓存中没有，尝试从主窗口获取同步系统参考窗口
            main_window = self._get_main_window()
            logger.info(f"【SYSREF频率获取】主窗口: {main_window is not None}")

            if main_window and hasattr(main_window, 'sync_sysref_window'):
                sync_window = main_window.sync_sysref_window
                logger.info(f"【SYSREF频率获取】同步系统参考窗口: {sync_window is not None}")

                if sync_window and hasattr(sync_window.ui, 'SyncSysrefFreq1'):
                    freq_text = sync_window.ui.SyncSysrefFreq1.text()
                    logger.info(f"【SYSREF频率获取】SyncSysrefFreq1控件值: '{freq_text}'")
                    if freq_text:
                        sysref_freq = float(freq_text)
                        logger.info(f"【SYSREF频率获取】✅ 从同步系统参考窗口获取SYSREF频率: {sysref_freq:.5f} MHz")
                        return sysref_freq
                    else:
                        logger.warning("【SYSREF频率获取】SyncSysrefFreq1控件值为空")
                else:
                    logger.warning("【SYSREF频率获取】同步系统参考窗口中未找到SyncSysrefFreq1控件")
            else:
                logger.warning("【SYSREF频率获取】同步系统参考窗口未打开或不存在")

            # 如果无法从窗口获取，尝试使用合理的默认值
            # 临时解决方案：使用一个合理的默认SYSREF频率
            default_sysref_freq = 245.76  # MHz，基于常见的VCO频率2949.12/12 ≈ 245.76
            logger.warning(f"【SYSREF频率获取】❌ 无法获取SYSREF频率，使用默认值: {default_sysref_freq} MHz")
            logger.info("【SYSREF频率获取】💡 建议：打开同步系统参考窗口并设置正确的VCO频率和分频器")
            return default_sysref_freq

        except Exception as e:
            logger.error(f"【SYSREF频率获取】获取SYSREF频率时出错: {str(e)}")
            return 0.0

    def _calculate_sysref_frequency_from_pll2pfd(self):
        """从PLL2PFD主动计算SYSREF频率

        计算逻辑：
        1. 获取PLL2PFD频率
        2. 使用默认的SYSREF分频器值（通常为12）
        3. 计算SYSREF频率 = PLL2PFD频率

        Returns:
            float: 计算得到的SYSREF频率，如果计算失败返回0
        """
        try:
            logger.info("【主动计算SYSREF】开始从PLL2PFD计算SYSREF频率...")

            # 获取PLL2PFD频率
            if hasattr(self.ui, 'PLL2PFDFreq'):
                pll2_pfd_text = self.ui.PLL2PFDFreq.text()
                logger.info(f"【主动计算SYSREF】PLL2PFD控件值: '{pll2_pfd_text}'")

                if pll2_pfd_text and pll2_pfd_text.strip():
                    try:
                        pll2_pfd_freq = float(pll2_pfd_text)
                        logger.info(f"【主动计算SYSREF】PLL2PFD频率: {pll2_pfd_freq:.5f} MHz")

                        # 根据用户的说明，SYSREF频率应该等于PLL2PFD频率
                        # 因为 InternalVCO = PLL2PFD × SYSREF_DIV
                        # 而 SYSREF频率 = InternalVCO / SYSREF_DIV = PLL2PFD
                        sysref_freq = pll2_pfd_freq

                        logger.info(f"【主动计算SYSREF】✅ 计算得到SYSREF频率: {sysref_freq:.5f} MHz")
                        logger.info(f"【主动计算SYSREF】计算逻辑: SYSREF频率 = PLL2PFD频率 = {pll2_pfd_freq:.5f} MHz")

                        return sysref_freq

                    except ValueError:
                        logger.warning(f"【主动计算SYSREF】PLL2PFD值无法转换为数字: '{pll2_pfd_text}'")
                else:
                    logger.warning("【主动计算SYSREF】PLL2PFD控件值为空")
                    logger.info("【主动计算SYSREF】💡 提示：PLL2PFD可能还没有计算，这通常发生在PLL窗口刚打开时")
            else:
                logger.warning("【主动计算SYSREF】PLL2PFD控件不存在")

            logger.warning("【主动计算SYSREF】❌ 无法计算SYSREF频率")
            return 0.0

        except Exception as e:
            logger.error(f"【主动计算SYSREF】计算SYSREF频率时出错: {str(e)}")
            return 0.0







        except Exception as e:
            logger.error(f"获取SYSREF频率时出错: {str(e)}")
            return 0.0

    def _get_sysref_divider_value(self):
        """获取SYSREF分频器的值

        优先从缓存获取，然后从同步系统参考窗口获取SYSREF_DIV的值

        Returns:
            float: SYSREF分频器值，默认为1.0
        """
        try:
            # 首先尝试从事件总线缓存获取
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            if bus and hasattr(bus, 'get_cached_sysref_div'):
                cached_div = bus.get_cached_sysref_div()
                if cached_div is not None:
                    logger.debug(f"从事件总线缓存获取SYSREF分频器值: {cached_div}")
                    return float(cached_div)

            # 如果缓存中没有，尝试从主窗口获取同步系统参考窗口
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'sync_sysref_window') and main_window.sync_sysref_window:
                sync_window = main_window.sync_sysref_window
                if hasattr(sync_window.ui, 'spinBoxSysrefDIV'):
                    sysref_div = sync_window.ui.spinBoxSysrefDIV.value()
                    if sysref_div > 0:
                        logger.debug(f"从同步系统参考窗口获取SYSREF分频器值: {sysref_div}")
                        return float(sysref_div)
                    else:
                        logger.warning("SYSREF分频器值为0或负数，使用默认值1")
                        return 1.0
                else:
                    logger.warning("同步系统参考窗口中未找到spinBoxSysrefDIV控件")
            else:
                logger.warning("【SYSREF分频器获取】同步系统参考窗口未打开或不存在，且缓存中无SYSREF分频器值，尝试从RegisterManager获取...")

                # 尝试从RegisterManager获取SYSREF分频器值作为备用方案
                try:
                    if hasattr(self, 'register_manager') and self.register_manager:
                        # SYSREF_DIV在寄存器0x4A中，位字段为SYSREF_DIV[12:0]
                        reg_value = self.register_manager.get_register_value("0x4A")
                        if reg_value is not None:
                            # SYSREF_DIV[12:0]位于寄存器的低13位
                            sysref_div_value = reg_value & 0x1FFF  # 提取低13位

                            # 添加合理性检查：SYSREF分频器通常在1-4096范围内
                            if sysref_div_value > 0 and sysref_div_value <= 4096:
                                logger.info(f"【SYSREF分频器获取】✅ 从RegisterManager获取SYSREF_DIV: {sysref_div_value}")
                                # 缓存这个值供后续使用
                                if bus and hasattr(bus, 'cache_sysref_data'):
                                    bus.cache_sysref_data(None, sysref_div_value)
                                return float(sysref_div_value)
                            elif sysref_div_value > 4096:
                                logger.warning(f"【SYSREF分频器获取】⚠️ 从寄存器获取的SYSREF_DIV值异常大: {sysref_div_value}，使用默认值12")
                                logger.warning(f"【SYSREF分频器获取】寄存器0x4A原始值: 0x{reg_value:04X}")
                                return 12.0  # 使用合理的默认值
                            else:
                                logger.warning("【SYSREF分频器获取】SYSREF_DIV值为0，使用默认值12")
                                return 12.0
                except Exception as e:
                    logger.debug(f"从RegisterManager获取SYSREF分频器值失败: {str(e)}")

            # 如果无法获取，返回合理的默认值12
            logger.info("【SYSREF分频器获取】使用默认值12，建议打开同步系统参考窗口或检查RegisterManager配置")
            return 12.0

        except Exception as e:
            logger.error(f"获取SYSREF分频器值时出错: {str(e)}")
            return 12.0

    def _get_dclk8_9div_value(self):
        """从ModernClkOutputsHandler获取DCLK8_9DIV控件的值

        Returns:
            int: DCLK8_9DIV的分频比值
        """
        try:
            # 尝试从主窗口获取ModernClkOutputsHandler实例
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'get_tool_window'):
                clk_outputs_handler = main_window.get_tool_window('ModernClkOutputsHandler')
                if clk_outputs_handler and hasattr(clk_outputs_handler.ui, 'DCLK8_9DIV'):
                    dclk8_9div_value = clk_outputs_handler.ui.DCLK8_9DIV.value()
                    logger.debug(f"从ModernClkOutputsHandler获取DCLK8_9DIV值: {dclk8_9div_value}")
                    return dclk8_9div_value
                else:
                    logger.warning("ModernClkOutputsHandler实例或DCLK8_9DIV控件不存在")

            # 尝试从寄存器获取DCLK8_9DIV值
            logger.debug("🔄 尝试从寄存器获取DCLK8_9DIV值...")
            if self.register_manager:
                # 尝试不同的寄存器地址和位字段名称
                possible_locations = [
                    ("0x30", "DCLK8_9_DIV[9:0]"),  # 根据DCLK6_7DIV在0x28，推测DCLK8_9DIV可能在0x30
                    ("0x67", "DCLK8_9_DIV[9:0]"),  # 原来的位置
                    ("0x30", "DCLK8_9DIV[9:0]"),
                    ("0x67", "DCLK8_9DIV[9:0]")
                ]

                for reg_addr, bit_field in possible_locations:
                    try:
                        dclk8_9div_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
                        if dclk8_9div_value is not None and dclk8_9div_value > 0:
                            logger.info(f"✅ 从寄存器{reg_addr}[{bit_field}]获取DCLK8_9DIV值: {dclk8_9div_value}")
                            return dclk8_9div_value
                    except Exception as e:
                        logger.debug(f"从寄存器{reg_addr}[{bit_field}]获取失败: {str(e)}")

                logger.warning("❌ 从所有可能的寄存器位置获取DCLK8_9DIV值都失败")
            else:
                logger.warning("❌ 寄存器管理器不存在")

            # 如果都无法获取，使用默认值
            logger.warning("⚠️ 无法获取DCLK8_9DIV值，使用默认值8")
            return 8

        except Exception as e:
            logger.error(f"获取DCLK8_9DIV值时出错: {str(e)}")
            return 8  # 返回默认值

    def _get_sysref_divider_frequency(self):
        """获取SYSREF分频器频率
        
        计算公式: SYSREF分频器频率 = VCODistFreq / SYSREF_DIV
        
        Returns:
            float: SYSREF分频器频率值
        """
        try:
            logger.debug("【SYSREF分频器频率】开始计算SYSREF分频器频率...")
            
            # 1. 获取VCODistFreq的值
            vco_dist_freq = 0.0
            if hasattr(self.ui, "VCODistFreq"):
                vco_dist_freq = self._get_float_from_lineedit(self.ui.VCODistFreq)
                logger.debug(f"【SYSREF分频器频率】VCODistFreq值: {vco_dist_freq} MHz")
            else:
                logger.warning("【SYSREF分频器频率】VCODistFreq控件不存在")
                return 0.0
            
            if vco_dist_freq <= 0:
                logger.warning("【SYSREF分频器频率】VCODistFreq值为0或负数，无法计算")
                return 0.0
            
            # 2. 获取SYSREF分频器值
            sysref_div = self._get_sysref_divider_value()
            logger.debug(f"【SYSREF分频器频率】SYSREF分频器值: {sysref_div}")
            
            if sysref_div <= 0:
                logger.warning("【SYSREF分频器频率】SYSREF分频器值为0或负数，无法计算")
                return 0.0
            
            # 3. 计算SYSREF分频器频率
            sysref_divider_freq = vco_dist_freq / sysref_div
            logger.debug(f"【SYSREF分频器频率】计算公式: {vco_dist_freq} / {sysref_div} = {sysref_divider_freq:.6f} MHz")
            
            return sysref_divider_freq
            
        except Exception as e:
            logger.error(f"【SYSREF分频器频率】获取SYSREF分频器频率时出错: {str(e)}")
            return 0.0

    def _get_external_feedback_frequency(self):
        """获取外部反馈频率
        
        尝试从以下来源获取外部反馈频率:
        1. 专用的外部反馈频率控件
        2. ExternalVCXOFreq控件
        3. 配置文件或默认值
        
        Returns:
            float: 外部反馈频率值
        """
        try:
            logger.debug("【外部反馈频率】开始获取外部反馈频率...")
            
            # 方法1: 尝试从专用的外部反馈频率控件获取
            external_feedback_controls = [
                "ExternalFeedbackFreq",
                "ExtFeedbackFreq", 
                "FBExtFreq",
                "ExternalFBFreq"
            ]
            
            for control_name in external_feedback_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    if hasattr(control, 'text'):
                        freq_text = control.text()
                        if freq_text and freq_text.strip():
                            try:
                                freq_value = float(freq_text)
                                logger.info(f"【外部反馈频率】✅ 从{control_name}控件获取外部反馈频率: {freq_value} MHz")
                                return freq_value
                            except ValueError:
                                logger.warning(f"【外部反馈频率】{control_name}控件值无法转换为数字: '{freq_text}'")
                    elif hasattr(control, 'value'):
                        freq_value = control.value()
                        if freq_value > 0:
                            logger.info(f"【外部反馈频率】✅ 从{control_name}控件获取外部反馈频率: {freq_value} MHz")
                            return freq_value
            
            # 方法2: 尝试从ExternalVCXOFreq控件获取（作为备选）
            if hasattr(self.ui, "ExternalVCXOFreq"):
                freq_text = self.ui.ExternalVCXOFreq.text()
                if freq_text and freq_text.strip():
                    try:
                        freq_value = float(freq_text)
                        logger.info(f"【外部反馈频率】✅ 从ExternalVCXOFreq控件获取外部反馈频率: {freq_value} MHz")
                        return freq_value
                    except ValueError:
                        logger.warning(f"【外部反馈频率】ExternalVCXOFreq控件值无法转换为数字: '{freq_text}'")
            
            # 方法3: 尝试从OSCinFreq控件获取（作为最后备选）
            if hasattr(self.ui, "OSCinFreq"):
                freq_text = self.ui.OSCinFreq.text()
                if freq_text and freq_text.strip():
                    try:
                        freq_value = float(freq_text)
                        logger.info(f"【外部反馈频率】✅ 从OSCinFreq控件获取外部反馈频率: {freq_value} MHz")
                        return freq_value
                    except ValueError:
                        logger.warning(f"【外部反馈频率】OSCinFreq控件值无法转换为数字: '{freq_text}'")
            
            # 方法4: 使用默认值
            default_external_freq = 122.88  # MHz，基于常见的外部时钟频率
            logger.warning(f"【外部反馈频率】❌ 无法获取外部反馈频率，使用默认值: {default_external_freq} MHz")
            logger.info("【外部反馈频率】💡 建议：在UI中添加专用的外部反馈频率输入控件")
            return default_external_freq
            
        except Exception as e:
            logger.error(f"【外部反馈频率】获取外部反馈频率时出错: {str(e)}")
            return 0.0

    def _calculate_pll1_output_with_source(self, input_freq):
        """使用指定的输入频率计算PLL1输出"""
        try:
            if not hasattr(self.ui, "PLL1PFDFreq"):
                return 0.0

            # 检查PLL1是否掉电
            if hasattr(self.ui, "PLL1PD") and self.ui.PLL1PD.isChecked():
                self.ui.PLL1PFDFreq.setText("0.00")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL1RDividerSetting"):
                r_div = max(1, self.ui.PLL1RDividerSetting.value())

            # 计算PFD频率
            if r_div > 0:
                pll1_pfd_freq = input_freq / r_div
                self.ui.PLL1PFDFreq.setText(f"{pll1_pfd_freq:.5f}")
                logger.debug(f"PLL1 PFD频率: {pll1_pfd_freq:.5f} MHz (输入: {input_freq}, R: {r_div})")

                # 重新计算PLL1NDivider（基于当前的PLL1NclkMux设置）
                if hasattr(self.ui, "PLL1NclkMux"):
                    current_mux_value = self._get_pll1_nclk_mux_value()
                    logger.debug(f"重新计算PLL1NDivider，当前MUX值: {current_mux_value}")
                    self._adjust_pll1_n_divider_for_mux(current_mux_value)

                # 通知其他窗口PLL1PFDFreq值已更新
                self._notify_pll1_pfd_freq_changed(pll1_pfd_freq)

                return pll1_pfd_freq
            else:
                self.ui.PLL1PFDFreq.setText("0.00")
                logger.warning("PLL1 R Divider 为0，无法计算PFD频率")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL1输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("Error")
            self._notify_pll1_pfd_freq_changed(0.0)
            return 0.0

    def _calculate_pll2_output_with_source(self, input_freq):
        """根据PLL2RclkMux选择计算PLL2输出

        根据PLL2RclkMux的不同选择使用不同的计算公式:
        - PLL2RclkMux = 0 (OSCin): PLL2PFDFreq = OSCin × Doubler / PLL2RDivider
        - PLL2RclkMux = 1 (PLL1 CLKin): PLL2PFDFreq = FreFin / PLL2NDivider

        Args:
            input_freq: PLL2的参考输入频率 (当PLL2RclkMux=0时使用，PLL2RclkMux=1时会忽略此参数)

        Returns:
            float: PLL2PFDFreq值
        """
        try:
            if not hasattr(self.ui, "PLL2PFDFreq"):
                return 0.0

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.PLL2PFDFreq.setText("0.00")
                return 0.0

            # 使用统一的PLL2计算公式
            return self._calculate_pll2_unified_formula(input_freq)

        except Exception as e:
            logger.error(f"计算PLL2输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL2PFDFreq"):
                self.ui.PLL2PFDFreq.setText("Error")
            return 0.0

    def _calculate_pll2_unified_formula(self, input_freq):
        """使用简化的PLL2计算公式

        简化公式: 直接使用PLL2Cin输入值计算VCODistFreq，不进行反馈公式平衡
        VCODistFreq = PLL2Cin × PLL2NDivider (当PLL2NclkMux=1时)
        VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler (当PLL2NclkMux=0时)

        Args:
            input_freq: PLL2的参考输入频率

        Returns:
            float: PLL2PFDFreq值
        """
        try:
            logger.debug("使用简化的PLL2计算公式")

            # 1. 计算PLL2PFDFreq (左边部分)
            pll2_pfd_freq = self._calculate_pll2_pfd_frequency(input_freq)
            if pll2_pfd_freq <= 0:
                self._update_pll2_related_outputs(0.0)
                return 0.0

            # 2. 设置PLL2PFDFreq显示值
            self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")

            # 2.1. 缓存PLL2PFD频率值，供同步系统参考窗口使用
            self._cache_pll2_pfd_frequency(pll2_pfd_freq)

            # 3. 使用简化方法更新VCODistFreq
            self._update_vco_dist_freq_simplified(pll2_pfd_freq)

            logger.debug(f"PLL2简化公式计算完成: PFDFreq={pll2_pfd_freq:.3f} MHz")
            return pll2_pfd_freq

        except Exception as e:
            logger.error(f"PLL2简化公式计算时出错: {str(e)}")
            self._update_pll2_related_outputs(0.0)
            return 0.0

    def _update_vco_dist_freq_simplified(self, pll2_pfd_freq):
        """使用简化方法更新VCODistFreq值

        根据PLL2NclkMux模式选择不同的计算方法:
        - 模式0 (PLL2 Prescaler): VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler
        - 模式1 (Feedback Mux): VCODistFreq = PLL2Cin × PLL2NDivider

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                logger.warning("VCODistFreq控件不存在")
                return

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)
                return

            # 获取PLL2NDivider值
            n_divider = self._get_pll2_n_divider_value()
            if n_divider <= 0:
                logger.warning("PLL2NDivider值无效")
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)
                return

            # 根据PLL2NclkMux模式选择计算方法
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()

            if pll2_nclk_mux == 0:
                # 模式0: PLL2 Prescaler模式
                # VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler
                prescaler_val = self._get_pll2_prescaler_value()
                if pll2_pfd_freq > 0 and prescaler_val > 0:
                    vco_dist_freq = pll2_pfd_freq * n_divider * prescaler_val
                    self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                    logger.debug(f"VCODistFreq简化计算 (Prescaler模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_divider}, Prescaler: {prescaler_val})")
                    self._notify_vco_dist_freq_changed(vco_dist_freq)
                else:
                    self.ui.VCODistFreq.setText("0.00")
                    logger.warning(f"VCODistFreq计算参数无效 (Prescaler模式): PFD={pll2_pfd_freq}, N={n_divider}, Prescaler={prescaler_val}")
                    self._notify_vco_dist_freq_changed(0.0)

            elif pll2_nclk_mux == 1:
                # 模式1: Feedback Mux模式
                # 需要根据FBMUX值选择不同的计算方法
                fb_mux_value = 0
                if hasattr(self.ui, "FBMUX"):
                    fb_mux_value = self.ui.FBMUX.currentData() if hasattr(self.ui.FBMUX, 'currentData') else self.ui.FBMUX.currentIndex()

                if fb_mux_value == 2:  # SYSREF Divider
                    # VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv
                    sysref_div = self._get_sysref_divider_value()
                    if pll2_pfd_freq > 0 and sysref_div > 0:
                        vco_dist_freq = pll2_pfd_freq * n_divider * sysref_div
                        self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                        logger.debug(f"VCODistFreq简化计算 (SYSREF Divider模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_divider}, SysrefDiv: {sysref_div})")
                        self._notify_vco_dist_freq_changed(vco_dist_freq)
                    else:
                        self.ui.VCODistFreq.setText("0.00")
                        logger.warning(f"VCODistFreq计算参数无效 (SYSREF Divider模式): PFD={pll2_pfd_freq}, N={n_divider}, SysrefDiv={sysref_div}")
                        self._notify_vco_dist_freq_changed(0.0)
                else:
                    # 其他Feedback模式: VCODistFreq = PLL2Cin × PLL2NDivider
                    pll2_cin_freq = self._get_pll2_cin_frequency()
                    if pll2_cin_freq > 0:
                        vco_dist_freq = pll2_cin_freq * n_divider
                        self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                        logger.debug(f"VCODistFreq简化计算 (Feedback模式): {vco_dist_freq:.5f} MHz (PLL2Cin: {pll2_cin_freq}, N: {n_divider})")
                        self._notify_vco_dist_freq_changed(vco_dist_freq)
                    else:
                        self.ui.VCODistFreq.setText("0.00")
                        logger.warning(f"VCODistFreq计算参数无效 (Feedback模式): PLL2Cin={pll2_cin_freq}, N={n_divider}")
                        self._notify_vco_dist_freq_changed(0.0)
            else:
                logger.warning(f"未知的PLL2NclkMux值: {pll2_nclk_mux}")
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)

        except Exception as e:
            logger.error(f"简化VCODistFreq计算时出错: {str(e)}")
            self.ui.VCODistFreq.setText("0.00")
            self._notify_vco_dist_freq_changed(0.0)

    def _get_pll2_cin_frequency(self):
        """获取PLL2Cin控件显示的频率值

        Returns:
            float: PLL2Cin的频率值
        """
        try:
            if hasattr(self.ui, "PLL2Cin"):
                cin_text = self.ui.PLL2Cin.text().strip()
                if cin_text:
                    return float(cin_text)
            return 0.0
        except (ValueError, AttributeError) as e:
            logger.error(f"获取PLL2Cin频率值时出错: {str(e)}")
            return 0.0

    def _update_pll2_related_outputs(self, pll2_pfd_freq):
        """更新PLL2相关的所有输出值

        当PLL2参数变化时，需要更新：
        1. VCODistFreq
        2. InternalVCOFreq (如果存在)
        3. Fin0Freq
        4. 其他依赖于PLL2的输出

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            logger.debug(f"更新PLL2相关输出值，PLL2PFDFreq={pll2_pfd_freq:.6f} MHz")

            # 1. 更新VCODistFreq
            self._update_vco_dist_freq(pll2_pfd_freq)

            # 2. 更新InternalVCOFreq (与VCODistFreq同步)
            self._update_internal_vco_freq()

            # 3. 更新Fin0Freq
            self._update_fin0_freq(pll2_pfd_freq)

            # 4. 通知PLL2PFDFreq变化
            self._notify_pll2_pfd_freq_changed(pll2_pfd_freq)

            logger.debug("PLL2相关输出值更新完成")

        except Exception as e:
            logger.error(f"更新PLL2相关输出值时出错: {str(e)}")

    def _update_vco_dist_freq(self, pll2_pfd_freq):
        """更新VCODistFreq值

        使用统一公式计算: VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                logger.warning("VCODistFreq控件不存在")
                return

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)
                return

            # 获取PLL2NDivider值
            n_divider = self._get_pll2_n_divider_value()

            # 获取PLL2Prescaler值
            prescaler_val = self._get_pll2_prescaler_value()

            # 根据PLL2NclkMux模式选择不同的计算公式
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()

            if pll2_nclk_mux == 0:
                # 模式0: PLL2 Prescaler模式
                if pll2_pfd_freq > 0 and n_divider > 0 and prescaler_val > 0:
                    vco_dist_freq = pll2_pfd_freq * n_divider * prescaler_val
                    self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                    logger.debug(f"VCODistFreq更新 (Prescaler模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_divider}, Prescaler: {prescaler_val})")
                    self._notify_vco_dist_freq_changed(vco_dist_freq)
                else:
                    self.ui.VCODistFreq.setText("0.00")
                    logger.warning(f"VCODistFreq计算参数无效 (Prescaler模式): PFD={pll2_pfd_freq}, N={n_divider}, Prescaler={prescaler_val}")
                    self._notify_vco_dist_freq_changed(0.0)

            elif pll2_nclk_mux == 1:
                # 模式1: Feedback Mux模式
                # 获取FB_MUX值
                fb_mux_value = 0
                if hasattr(self.ui, "FBMUX"):
                    if hasattr(self.ui.FBMUX, 'currentData'):
                        fb_mux_value = self.ui.FBMUX.currentData() or 0
                    else:
                        fb_mux_value = self.ui.FBMUX.currentIndex()

                feedback_divider = 1.0

                if fb_mux_value == 0:  # CLKout6
                    feedback_divider = self._get_dclk6_7div_value()
                elif fb_mux_value == 1:  # CLKout8
                    feedback_divider = self._get_dclk8_9div_value()
                else:
                    feedback_divider = 1.0

                if pll2_pfd_freq > 0 and n_divider > 0 and feedback_divider > 0:
                    vco_dist_freq = pll2_pfd_freq * n_divider * feedback_divider
                    self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                    logger.debug(f"VCODistFreq更新 (Feedback模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_divider}, 反馈分频: {feedback_divider})")
                    self._notify_vco_dist_freq_changed(vco_dist_freq)
                else:
                    self.ui.VCODistFreq.setText("0.00")
                    logger.warning(f"VCODistFreq计算参数无效 (Feedback模式): PFD={pll2_pfd_freq}, N={n_divider}, 反馈分频={feedback_divider}")
                    self._notify_vco_dist_freq_changed(0.0)
            else:
                logger.warning(f"未知的PLL2NclkMux值: {pll2_nclk_mux}")
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)

        except Exception as e:
            logger.error(f"更新VCODistFreq时出错: {str(e)}")
            if hasattr(self.ui, "VCODistFreq"):
                self.ui.VCODistFreq.setText("Error")

    def _get_pll2_prescaler_value(self):
        """获取PLL2Prescaler的值

        Returns:
            float: PLL2Prescaler的值
        """
        try:
            if hasattr(self.ui, "PLL2Prescaler"):
                try:
                    prescaler_text = self.ui.PLL2Prescaler.currentText()
                    prescaler_val = float(prescaler_text) if prescaler_text else 1.0
                    logger.debug(f"PLL2Prescaler值: {prescaler_val}")
                    return prescaler_val
                except ValueError:
                    logger.error(f"无法将PLL2Prescaler值 '{prescaler_text}' 转换为数字")
                    return 1.0
            else:
                logger.debug("PLL2Prescaler控件不存在，使用默认值1.0")
                return 1.0

        except Exception as e:
            logger.error(f"获取PLL2Prescaler值时出错: {str(e)}")
            return 1.0

    def _update_internal_vco_freq(self):
        """更新InternalVCOFreq值，使其与VCODistFreq同步"""
        try:
            if hasattr(self.ui, "InternalVCOFreq") and hasattr(self.ui, "VCODistFreq"):
                vco_dist_freq_text = self.ui.VCODistFreq.text()
                self.ui.InternalVCOFreq.setText(vco_dist_freq_text)
                logger.debug(f"InternalVCOFreq已同步为: {vco_dist_freq_text} MHz")
            elif hasattr(self.ui, "InternalVCOFreq"):
                logger.debug("VCODistFreq控件不存在，无法同步InternalVCOFreq")
            else:
                logger.debug("InternalVCOFreq控件不存在")

        except Exception as e:
            logger.error(f"更新InternalVCOFreq时出错: {str(e)}")

    def _update_fin0_freq(self, pll2_pfd_freq):
        """更新Fin0Freq值

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            if hasattr(self.ui, "Fin0Freq"):
                # 检查当前VCO模式是否为Fin0模式
                vco_mode = 0
                if hasattr(self.ui, "comboVcoMode"):
                    if hasattr(self.ui.comboVcoMode, 'currentData'):
                        vco_mode = self.ui.comboVcoMode.currentData() or 0
                    else:
                        vco_mode = self.ui.comboVcoMode.currentIndex()

                if vco_mode == 3:  # Fin0模式
                    # 在Fin0模式下，Fin0Freq是用户输入控件，不应该被自动计算覆盖
                    logger.debug("当前为Fin0模式，跳过Fin0Freq自动更新（保持用户输入值）")
                else:
                    # 在其他模式下，Fin0Freq是计算结果显示控件
                    self.ui.Fin0Freq.setText(f"{pll2_pfd_freq:.3f}")
                    logger.debug(f"Fin0Freq更新为: {pll2_pfd_freq:.3f} MHz")
            else:
                logger.debug("Fin0Freq控件不存在")

        except Exception as e:
            logger.error(f"更新Fin0Freq时出错: {str(e)}")

    def _notify_pll2_pfd_freq_changed(self, pll2_pfd_freq):
        """通知其他窗口PLL2PFDFreq值已更新

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 缓存PLL2PFDFreq值
            if hasattr(bus, 'cache_pll2_pfd_freq'):
                bus.cache_pll2_pfd_freq(pll2_pfd_freq)
                logger.debug(f"已缓存PLL2PFDFreq值: {pll2_pfd_freq} MHz")

            # 发送PLL2PFDFreq更新信号
            if hasattr(bus, 'pll2_pfd_freq_updated'):
                bus.pll2_pfd_freq_updated.emit(pll2_pfd_freq)
                logger.debug(f"已发送PLL2PFDFreq更新信号: {pll2_pfd_freq} MHz")
            else:
                logger.debug("RegisterUpdateBus中缺少pll2_pfd_freq_updated信号")

        except Exception as e:
            logger.error(f"通知PLL2PFDFreq更新时发生错误: {str(e)}")

    def _calculate_pll2_pfd_frequency(self, input_freq):
        """计算PLL2PFDFreq (公式左边部分)

        根据PLL2RclkMux的选择使用不同的计算公式:
        - PLL2RclkMux = 0 (OSCin): PLL2PFDFreq = OSCin × Doubler / PLL2RDivider
        - PLL2RclkMux = 1 (PLL1 CLKin): PLL2PFDFreq = FreFin / PLL2NDivider

        Args:
            input_freq: 输入频率 (根据PLL2RclkMux选择而定)

        Returns:
            float: PLL2PFDFreq值
        """
        try:
            # 获取PLL2RclkMux的当前值
            pll2_rclk_mux = 0
            if hasattr(self.ui, "PLL2RclkMux"):
                if hasattr(self.ui.PLL2RclkMux, 'currentData'):
                    pll2_rclk_mux = self.ui.PLL2RclkMux.currentData() or 0
                else:
                    pll2_rclk_mux = self.ui.PLL2RclkMux.currentIndex()
                logger.debug(f"PLL2RclkMux值: {pll2_rclk_mux}")

            if pll2_rclk_mux == 0:
                # OSCin模式：使用原有的计算公式
                # 获取倍频器值
                doubler_value = 1
                if hasattr(self.ui, "Doubler"):
                    doubler_value = self.ui.Doubler.currentIndex() + 1  # 0->1, 1->2

                # 获取R分频器值
                r_divider = 1
                if hasattr(self.ui, "PLL2RDivider"):
                    r_divider = max(1, self.ui.PLL2RDivider.value())

                # 计算PFD频率
                if r_divider > 0:
                    pll2_pfd_freq = input_freq * doubler_value / r_divider
                    logger.debug(f"PLL2PFDFreq计算 (OSCin模式): {input_freq} × {doubler_value} / {r_divider} = {pll2_pfd_freq:.6f} MHz")
                    return pll2_pfd_freq
                else:
                    logger.warning("PLL2 R Divider 为0，无法计算PFD频率")
                    return 0.0

            elif pll2_rclk_mux == 1:
                # PLL1 CLKin模式：使用FreFin / PLL2RDivider = PLL2PFD的公式
                # 获取FreFin频率值
                fre_fin_freq = 0.0
                if hasattr(self.ui, "FreFin"):
                    fre_fin_text = self.ui.FreFin.text()
                    try:
                        fre_fin_freq = float(fre_fin_text) if fre_fin_text else 0.0
                    except ValueError:
                        logger.warning(f"FreFin值无效: {fre_fin_text}")
                        fre_fin_freq = 0.0

                # 获取PLL2NDivider值
                pll2_r_divider = 1
                if hasattr(self.ui, "PLL2RDivider"):
                    pll2_r_divider = max(1, self.ui.PLL2RDivider.value())

                # 计算PFD频率
                if fre_fin_freq > 0 and pll2_r_divider > 0:
                    pll2_pfd_freq = fre_fin_freq / pll2_r_divider
                    logger.debug(f"PLL2PFDFreq计算 (PLL1 CLKin模式): {fre_fin_freq} / {pll2_r_divider} = {pll2_pfd_freq:.6f} MHz")
                    return pll2_pfd_freq
                else:
                    logger.warning(f"PLL1 CLKin模式参数无效: FreFin={fre_fin_freq}, PLL2NDivider={pll2_r_divider}")
                    return 0.0

            else:
                logger.warning(f"未知的PLL2RclkMux值: {pll2_rclk_mux}，使用OSCin模式")
                # 默认使用OSCin模式
                doubler_value = 1
                if hasattr(self.ui, "Doubler"):
                    doubler_value = self.ui.Doubler.currentIndex() + 1

                r_divider = 1
                if hasattr(self.ui, "PLL2RDivider"):
                    r_divider = max(1, self.ui.PLL2RDivider.value())

                if r_divider > 0:
                    pll2_pfd_freq = input_freq * doubler_value / r_divider
                    logger.debug(f"PLL2PFDFreq计算 (默认OSCin模式): {input_freq} × {doubler_value} / {r_divider} = {pll2_pfd_freq:.6f} MHz")
                    return pll2_pfd_freq
                else:
                    return 0.0

        except Exception as e:
            logger.error(f"计算PLL2PFDFreq时出错: {str(e)}")
            return 0.0

    def _get_pll2_n_divider_input_frequency(self):
        """获取PLL2NDivider的输入频率

        根据PLL2NclkMux的值确定输入来源:
        - 0: PLL2 Prescaler输出
        - 1: Feedback Mux输出 (CLKout6/CLKout8等)

        Returns:
            float: PLL2NDivider的输入频率
        """
        try:
            # 获取PLL2NclkMux的值
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()

            if pll2_nclk_mux == 0:
                # PLL2 Prescaler输出
                prescaler_freq = self._get_pll2_prescaler_output_frequency()
                logger.debug(f"PLL2NDivider输入来源: PLL2 Prescaler, 频率: {prescaler_freq:.6f} MHz")
                return prescaler_freq
            elif pll2_nclk_mux == 1:
                # Feedback Mux输出
                feedback_freq = self._get_feedback_mux_frequency()
                logger.debug(f"PLL2NDivider输入来源: Feedback Mux, 频率: {feedback_freq:.6f} MHz")
                return feedback_freq
            else:
                logger.warning(f"未知的PLL2NclkMux值: {pll2_nclk_mux}")
                return 0.0

        except Exception as e:
            logger.error(f"获取PLL2NDivider输入频率时出错: {str(e)}")
            return 0.0

    def _get_pll2_n_divider_value(self):
        """获取PLL2NDivider的分频比值

        Returns:
            int: PLL2NDivider的值
        """
        try:
            if hasattr(self.ui, "PLL2NDivider"):
                n_divider_value = self.ui.PLL2NDivider.value()

                # 添加合理性检查：PLL2NDivider通常在1-262143范围内（18位）
                if n_divider_value > 0 and n_divider_value <= 262143:
                    logger.debug(f"PLL2NDivider值: {n_divider_value}")
                    return n_divider_value
                elif n_divider_value > 262143:
                    logger.warning(f"⚠️ PLL2NDivider值异常大: {n_divider_value}，使用默认值40")
                    return 40  # 使用合理的默认值
                else:
                    logger.warning("PLL2NDivider值为0或负数，使用默认值40")
                    return 40
            else:
                logger.warning("PLL2NDivider控件不存在")
                return 40  # 使用更合理的默认值

        except Exception as e:
            logger.error(f"获取PLL2NDivider值时出错: {str(e)}")
            return 40  # 使用更合理的默认值

    def _get_pll2_prescaler_output_frequency(self):
        """获取PLL2 Prescaler的输出频率

        Returns:
            float: PLL2 Prescaler输出频率
        """
        try:
            # PLL2 Prescaler的输出频率需要从VCO频率计算
            # 这里先返回一个基于VCODistFreq的估算值
            # 实际实现可能需要更复杂的计算

            if hasattr(self.ui, "VCODistFreq"):
                vco_dist_freq = self._get_float_from_lineedit(self.ui.VCODistFreq)

                # 获取PLL2 Prescaler的分频比 (通常是固定值，如2或4)
                prescaler_ratio = self._get_pll2_prescaler_ratio()

                if prescaler_ratio > 0:
                    prescaler_freq = vco_dist_freq / prescaler_ratio
                    logger.debug(f"PLL2 Prescaler频率: {vco_dist_freq} / {prescaler_ratio} = {prescaler_freq:.6f} MHz")
                    return prescaler_freq
                else:
                    logger.warning("PLL2 Prescaler分频比为0")
                    return 0.0
            else:
                logger.warning("VCODistFreq控件不存在")
                return 0.0

        except Exception as e:
            logger.error(f"获取PLL2 Prescaler输出频率时出错: {str(e)}")
            return 0.0

    def _get_pll2_prescaler_ratio(self):
        """获取PLL2 Prescaler的分频比

        Returns:
            int: PLL2 Prescaler的分频比
        """
        try:
            # PLL2 Prescaler的分频比通常是固定的
            # 这里可以从寄存器或控件获取，暂时使用默认值

            if self.register_manager:
                # 尝试从寄存器获取PLL2 Prescaler配置
                prescaler_config = self.register_manager.get_bit_field_value("0x83", "PLL2_PRE_R[11:0]")
                if prescaler_config is not None and prescaler_config > 0:
                    logger.debug(f"从寄存器获取PLL2 Prescaler分频比: {prescaler_config}")
                    return prescaler_config

            # 使用默认值
            default_ratio = 2
            logger.debug(f"使用默认PLL2 Prescaler分频比: {default_ratio}")
            return default_ratio

        except Exception as e:
            logger.error(f"获取PLL2 Prescaler分频比时出错: {str(e)}")
            return 2  # 默认值

    def _verify_pll2_formula_balance(self, pll2_pfd_freq, n_divider_input_freq, n_divider_value):
        """验证PLL2公式的平衡性

        验证: OSCin × Doubler / PLL2RDivider = PLL2NDivider输入值 × PLL2NDivider

        Args:
            pll2_pfd_freq: 公式左边的值
            n_divider_input_freq: PLL2NDivider的输入频率
            n_divider_value: PLL2NDivider的值
        """
        try:
            if n_divider_value > 0 and n_divider_input_freq > 0:
                # 计算公式右边的值
                right_side = n_divider_input_freq * n_divider_value

                # 计算差值和相对误差
                difference = abs(pll2_pfd_freq - right_side)
                relative_error = difference / pll2_pfd_freq * 100 if pll2_pfd_freq > 0 else 0

                logger.debug("PLL2公式验证:")
                logger.debug(f"  左边 (PFDFreq): {pll2_pfd_freq:.6f} MHz")
                logger.debug(f"  右边 (NDivInput × NDivider): {n_divider_input_freq:.6f} × {n_divider_value} = {right_side:.6f} MHz")
                logger.debug(f"  差值: {difference:.6f} MHz")
                logger.debug(f"  相对误差: {relative_error:.3f}%")

                if relative_error > 1.0:  # 1%误差阈值
                    logger.debug(f"PLL2公式不平衡，相对误差: {relative_error:.3f}%")
                    # 注释掉自动调整逻辑，让用户可以自由修改PLL2NDivider
                    # self._suggest_pll2_parameter_adjustment(pll2_pfd_freq, n_divider_input_freq, n_divider_value)
                else:
                    logger.debug("PLL2公式平衡，参数设置合理")
            else:
                logger.warning("无法验证PLL2公式平衡性，参数不完整")

        except Exception as e:
            logger.error(f"验证PLL2公式平衡性时出错: {str(e)}")

    def _suggest_pll2_parameter_adjustment(self, pll2_pfd_freq, n_divider_input_freq, n_divider_value):
        """建议PLL2参数调整

        Args:
            pll2_pfd_freq: 目标PFD频率
            n_divider_input_freq: 当前NDivider输入频率
            n_divider_value: 当前NDivider值
        """
        try:
            if n_divider_input_freq > 0:
                # 建议的NDivider值，确保不小于最小值1
                suggested_n_divider = pll2_pfd_freq / n_divider_input_freq
                rounded_suggestion = max(1, round(suggested_n_divider))

                logger.info("PLL2参数调整建议:")
                logger.info(f"  PLL2PFDFreq: {pll2_pfd_freq:.6f} MHz")
                logger.info(f"  NDivider输入频率: {n_divider_input_freq:.6f} MHz")
                logger.info(f"  当前PLL2NDivider: {n_divider_value}")
                logger.info(f"  建议PLL2NDivider: {rounded_suggestion} (精确值: {suggested_n_divider:.6f})")
                logger.info("  调整后可实现更好的公式平衡")

                # 检查计算是否合理
                if suggested_n_divider < 0.5:
                    logger.warning(f"⚠️ 计算出的PLL2NDivider值过小 ({suggested_n_divider:.6f})，可能存在配置问题")
                    logger.warning(f"   这通常意味着NDivider输入频率 ({n_divider_input_freq:.6f}) 远大于PLL2PFDFreq ({pll2_pfd_freq:.6f})")
                elif suggested_n_divider > 1000:
                    logger.warning(f"⚠️ 计算出的PLL2NDivider值过大 ({suggested_n_divider:.6f})，可能存在配置问题")

                # 检查是否在反馈模式下，如果是则自动调整
                pll2_nclk_mux = self._get_pll2_nclk_mux_value()
                logger.info(f"🔍 检查PLL2NclkMux值: {pll2_nclk_mux}")
                if pll2_nclk_mux == 1:  # Feedback Mux模式
                    logger.info("🎯 检测到反馈模式，自动调整PLL2NDivider")
                    self._update_pll2_n_divider_if_needed(suggested_n_divider)
                else:
                    logger.info(f"ℹ️ 非反馈模式 (PLL2NclkMux={pll2_nclk_mux})，不自动调整PLL2NDivider")

        except Exception as e:
            logger.error(f"建议PLL2参数调整时出错: {str(e)}")

    def _get_pll2_nclk_mux_value(self):
        """获取PLL2NclkMux控件的值"""
        try:
            if hasattr(self.ui, "PLL2NclkMux"):
                if hasattr(self.ui.PLL2NclkMux, 'currentData'):
                    return self.ui.PLL2NclkMux.currentData() or 0
                else:
                    return self.ui.PLL2NclkMux.currentIndex()
            return 0
        except Exception as e:
            logger.error(f"获取PLL2NclkMux值时出错: {str(e)}")
            return 0

    def _calculate_pll2_with_feedback_mode(self, input_freq):
        """反馈模式下的PLL2计算 - 反向计算PLL2NDivider"""
        try:
            logger.debug("PLL2使用反馈模式，进行反向计算")

            # 1. 获取反馈频率（CLKout6频率）
            feedback_freq = self._get_feedback_mux_frequency()
            logger.debug(f"反馈频率（CLKout6）: {feedback_freq} MHz")

            if feedback_freq <= 0:
                logger.warning("反馈频率为0，无法进行反向计算")
                return 0.0

            # 2. 获取R分频器和倍频器值
            r_div = 1
            if hasattr(self.ui, "PLL2RDivider"):
                r_div = max(1, self.ui.PLL2RDivider.value())

            doubler_value = 1
            if hasattr(self.ui, "Doubler"):
                doubler_value = self.ui.Doubler.currentIndex() + 1

            # 3. 计算参考频率
            if r_div > 0:
                ref_freq = input_freq * doubler_value / r_div
                logger.debug(f"参考频率: {input_freq} × {doubler_value} / {r_div} = {ref_freq} MHz")
            else:
                logger.warning("PLL2 R Divider 为0，无法计算")
                return 0.0

            # 4. 反向计算PLL2NDivider
            # 公式：feedback_freq × PLL2NDivider = PLL2PFDFreq
            # 在反馈模式下，PLL2PFDFreq应该等于参考频率
            if ref_freq > 0:
                calculated_n_divider = ref_freq / feedback_freq
                logger.debug(f"反向计算PLL2NDivider: {ref_freq} / {feedback_freq} = {calculated_n_divider}")

                # 5. 更新PLL2NDivider控件值（如果需要）
                self._update_pll2_n_divider_if_needed(calculated_n_divider)

                # 6. 设置PLL2PFDFreq
                self.ui.PLL2PFDFreq.setText(f"{ref_freq:.3f}")
                logger.debug(f"PLL2 PFD频率（反馈模式）: {ref_freq:.3f} MHz")

                # 缓存PLL2PFD频率值，供同步系统参考窗口使用
                self._cache_pll2_pfd_frequency(ref_freq)

                return ref_freq
            else:
                logger.warning("参考频率为0，无法计算PLL2NDivider")
                return 0.0

        except Exception as e:
            logger.error(f"反馈模式PLL2计算时出错: {str(e)}")
            return 0.0

    def _calculate_pll2_with_normal_mode(self, input_freq):
        """正常模式下的PLL2计算 - 前向计算"""
        try:
            logger.debug("PLL2使用正常模式，进行前向计算")

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL2RDivider"):
                r_div = max(1, self.ui.PLL2RDivider.value())

            # 获取倍频器值
            doubler_value = 1
            if hasattr(self.ui, "Doubler"):
                doubler_value = self.ui.Doubler.currentIndex() + 1  # 0->1, 1->2

            # 计算PFD频率
            if r_div > 0:
                pll2_pfd_freq = input_freq * doubler_value / r_div
                self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
                logger.debug(f"PLL2 PFD频率（正常模式）: {pll2_pfd_freq:.3f} MHz (输入: {input_freq}, Doubler: {doubler_value}, R: {r_div})")

                # 缓存PLL2PFD频率值，供同步系统参考窗口使用
                self._cache_pll2_pfd_frequency(pll2_pfd_freq)

                return pll2_pfd_freq
            else:
                self.ui.PLL2PFDFreq.setText("0.00")
                logger.warning("PLL2 R Divider 为0，无法计算PFD频率")
                return 0.0

        except Exception as e:
            logger.error(f"正常模式PLL2计算时出错: {str(e)}")
            return 0.0

    def _update_pll2_n_divider_if_needed(self, calculated_value):
        """根据需要更新PLL2NDivider控件值"""
        try:
            # 防止循环调用
            if getattr(self, '_updating_pll2_n_divider', False):
                logger.debug("正在更新PLL2NDivider，跳过重复调用")
                return

            if hasattr(self.ui, "PLL2NDivider"):
                current_value = self.ui.PLL2NDivider.value()

                # 将计算值四舍五入到最接近的整数，但确保不小于最小值1
                rounded_value = max(1, round(calculated_value))

                # 检查是否需要更新
                if abs(current_value - rounded_value) > 0.5:
                    logger.info(f"自动更新PLL2NDivider: 当前值={current_value}, 计算值={calculated_value:.3f}, 更新为={rounded_value}")

                    # 设置标志防止循环
                    self._updating_pll2_n_divider = True
                    try:
                        # 通过跨寄存器控件管理器更新PLL2NDivider值
                        if self.cross_register_manager and self.cross_register_manager.is_cross_register_widget("PLL2NDivider"):
                            logger.info(f"🔧 通过跨寄存器管理器更新PLL2NDivider: {rounded_value}")
                            success = self.cross_register_manager.update_registers("PLL2NDivider", rounded_value)
                            if success:
                                logger.info("✅ 跨寄存器管理器更新成功")
                            else:
                                logger.error("❌ 跨寄存器管理器更新失败")
                        else:
                            # 备用方案：直接更新控件值
                            logger.info(f"🔧 直接更新PLL2NDivider控件: {rounded_value}")
                            self.ui.PLL2NDivider.setValue(rounded_value)

                        # 同时发送通知
                        self._notify_n_divider_suggestion(rounded_value, calculated_value)
                    finally:
                        # 确保标志被重置
                        self._updating_pll2_n_divider = False
                else:
                    logger.debug(f"PLL2NDivider值合理: 当前值={current_value}, 计算值={calculated_value:.3f}")
            else:
                logger.warning("PLL2NDivider控件不存在")

        except Exception as e:
            logger.error(f"更新PLL2NDivider时出错: {str(e)}")
            # 确保在异常情况下也重置标志
            self._updating_pll2_n_divider = False

    def _notify_n_divider_suggestion(self, suggested_value, calculated_value):
        """通知用户建议的N分频器值"""
        try:
            # 这里可以发送信号或显示提示
            logger.info(f"PLL2NDivider建议值: {suggested_value} (精确计算值: {calculated_value:.6f})")

            # 可以通过事件总线发送建议值
            # RegisterUpdateBus.instance().suggest_n_divider_value.emit(suggested_value, calculated_value)

        except Exception as e:
            logger.error(f"通知N分频器建议值时出错: {str(e)}")

    def _calculate_with_traditional_method(self):
        """使用传统的计算方法（原有的计算逻辑，相当于DUAL MODE）"""
        try:
            logger.debug("使用传统计算方法（默认DUAL MODE行为）")

            # 获取输入频率
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0

            # 使用改进的计算方法，支持反馈模式的自动调整
            self._calculate_pll1_output(fin_freq)
            pll2_pfd_freq = self._calculate_pll2_output_with_source(oscin_freq)
            self._calculate_fin0_output(pll2_pfd_freq)
            self._calculate_vco_dist_freq(pll2_pfd_freq)

            # 更新PLL2Cin控件（如果可见）
            if hasattr(self.ui, "PLL2Cin") and self.ui.PLL2Cin.isVisible():
                self._update_pll2cin_value()

            logger.debug(f"传统计算完成: Fin={fin_freq}, OSCin={oscin_freq}")

        except Exception as e:
            logger.error(f"传统计算方法时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _ensure_mode_is_set(self, mode_name):
        """确保指定的模式已经正确设置到寄存器中

        Args:
            mode_name: 要设置的模式名称
        """
        try:
            logger.debug(f"确保模式 {mode_name} 已正确设置")

            # 尝试获取ModernSetModesHandler实例
            modes_handler = self._get_modes_handler()
            if modes_handler:
                # 通过ModernSetModesHandler设置模式
                modes_handler.set_mode(mode_name)
                logger.info(f"通过ModernSetModesHandler设置模式: {mode_name}")
            else:
                # 如果没有ModernSetModesHandler，则直接设置寄存器
                logger.warning("未找到ModernSetModesHandler，将直接设置寄存器")
                self._set_mode_registers_directly(mode_name)

        except Exception as e:
            logger.error(f"确保模式设置时出错: {str(e)}")

    def _get_modes_handler(self):
        """获取ModernSetModesHandler实例"""
        try:
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'get_tool_window'):
                return main_window.get_tool_window('ModernSetModesHandler')
            return None
        except Exception as e:
            logger.error(f"获取ModernSetModesHandler时出错: {str(e)}")
            return None

    def _set_mode_registers_directly(self, mode_name):
        """直接设置模式相关的寄存器

        Args:
            mode_name: 模式名称
        """
        try:
            # 这里可以复用ModernSetModesHandler中的模式配置
            from ui.handlers.ModernSetModesHandler import ModernSetModesHandler

            # 创建临时实例来获取模式配置
            temp_handler = ModernSetModesHandler(register_manager=self.register_manager)
            mode_settings = temp_handler._get_mode_settings(mode_name)

            if mode_settings:
                logger.info(f"直接设置模式 {mode_name} 的寄存器")
                for reg_addr, bit_settings in mode_settings.items():
                    for bit_name, bit_value in bit_settings.items():
                        try:
                            self.register_manager.set_bit_field_value(reg_addr, bit_name, bit_value)
                            logger.debug(f"设置 {reg_addr}.{bit_name} = {bit_value}")
                        except Exception as e:
                            logger.warning(f"设置 {reg_addr}.{bit_name} = {bit_value} 失败: {str(e)}")
            else:
                logger.error(f"未找到模式 {mode_name} 的配置")

        except Exception as e:
            logger.error(f"直接设置模式寄存器时出错: {str(e)}")

    def _calculate_dual_mode_frequencies(self, fin_freq, oscin_freq, mode_name):
        """计算双环路模式下的频率

        Args:
            fin_freq: Fin输入频率
            oscin_freq: OSCin输入频率
            mode_name: 具体的双环路模式名称
        """
        try:
            logger.debug(f"使用双环路模式计算: {mode_name}")

            # PLL1计算 - 在双环路模式下，PLL1使用Fin作为输入
            pll1_pfd_freq = self._calculate_pll1_output(fin_freq)

            # PLL2计算 - 根据具体模式确定输入源
            if mode_name in ["DualLoop0DealyNested", "DualLoop0DealyNestedandCasc"]:
                # 嵌套模式：PLL2可能使用PLL1的输出作为输入
                pll2_input_freq = self._get_pll2_input_freq_for_nested_mode(pll1_pfd_freq, oscin_freq)
            else:
                # 标准双环路模式：PLL2使用OSCin作为输入
                pll2_input_freq = oscin_freq

            pll2_pfd_freq = self._calculate_pll2_output_with_source(pll2_input_freq)

            # 计算其他输出频率
            self._calculate_fin0_output(pll2_pfd_freq)
            self._calculate_vco_dist_freq(pll2_pfd_freq)

        except Exception as e:
            logger.error(f"计算双环路模式频率时出错: {str(e)}")

    def _calculate_single_mode_frequencies(self, fin_freq, oscin_freq, mode_name):
        """计算单环路模式下的频率

        Args:
            fin_freq: Fin输入频率
            oscin_freq: OSCin输入频率
            mode_name: 具体的单环路模式名称
        """
        try:
            logger.debug(f"使用单环路模式计算: {mode_name}")

            # PLL1在单环路模式下被关闭
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("0.00")
                self._notify_pll1_pfd_freq_changed(0.0)

            # PLL2计算 - 单环路模式下PLL2是主要的PLL
            if mode_name == "SingleLoop0Dealy":
                # 零延迟模式可能有特殊的反馈路径
                pll2_input_freq = self._get_pll2_input_freq_for_zero_delay_mode(oscin_freq)
            else:
                # 标准单环路模式
                pll2_input_freq = oscin_freq

            pll2_pfd_freq = self._calculate_pll2_output_with_source(pll2_input_freq)

            # 计算其他输出频率
            self._calculate_fin0_output(pll2_pfd_freq)
            self._calculate_vco_dist_freq(pll2_pfd_freq)

        except Exception as e:
            logger.error(f"计算单环路模式频率时出错: {str(e)}")

    def _calculate_distribution_mode_frequencies(self, fin_freq, oscin_freq):
        """计算分配模式下的频率

        Args:
            fin_freq: Fin输入频率
            oscin_freq: OSCin输入频率
        """
        try:
            logger.debug("使用分配模式计算")

            # 在分配模式下，PLL1和PLL2都被关闭
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("0.00")
                self._notify_pll1_pfd_freq_changed(0.0)

            if hasattr(self.ui, "PLL2PFDFreq"):
                self.ui.PLL2PFDFreq.setText("0.00")

            if hasattr(self.ui, "Fin0Freq"):
                self.ui.Fin0Freq.setText("0.00")

            if hasattr(self.ui, "VCODistFreq"):
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)

            # 在分配模式下，主要是直接分配输入时钟
            logger.debug("分配模式：所有PLL输出设为0，使用直接时钟分配")

        except Exception as e:
            logger.error(f"计算分配模式频率时出错: {str(e)}")

    def _get_pll2_input_freq_for_nested_mode(self, pll1_pfd_freq, oscin_freq):
        """获取嵌套模式下PLL2的输入频率

        Args:
            pll1_pfd_freq: PLL1的PFD频率
            oscin_freq: OSCin频率

        Returns:
            float: PLL2的输入频率
        """
        try:
            # 在嵌套模式下，需要检查PLL2_RCLK_MUX的设置
            if self.register_manager:
                pll2_rclk_mux = self.register_manager.get_bit_field_value("0x4F", "PLL2_RCLK_MUX") or 0
                if pll2_rclk_mux == 1:
                    # PLL2使用PLL1的输出作为参考时钟
                    logger.debug(f"嵌套模式：PLL2使用PLL1输出作为输入，频率={pll1_pfd_freq}")
                    return pll1_pfd_freq

            # 默认使用OSCin
            logger.debug(f"嵌套模式：PLL2使用OSCin作为输入，频率={oscin_freq}")
            return oscin_freq

        except Exception as e:
            logger.error(f"获取嵌套模式PLL2输入频率时出错: {str(e)}")
            return oscin_freq

    def _get_pll2_input_freq_for_zero_delay_mode(self, oscin_freq):
        """获取零延迟模式下PLL2的输入频率

        Args:
            oscin_freq: OSCin频率

        Returns:
            float: PLL2的输入频率
        """
        try:
            # 在零延迟模式下，可能有反馈路径影响输入频率
            # 这里可以根据具体的反馈配置来调整
            logger.debug(f"零延迟模式：PLL2使用OSCin作为输入，频率={oscin_freq}")
            return oscin_freq

        except Exception as e:
            logger.error(f"获取零延迟模式PLL2输入频率时出错: {str(e)}")
            return oscin_freq

    def _calculate_pll1_output(self, fin_freq):
        """计算PLL1的PFD频率"""
        try:
            if not hasattr(self.ui, "PLL1PFDFreq"):
                return 0.0

            # 检查PLL1是否掉电
            if hasattr(self.ui, "PLL1PD") and self.ui.PLL1PD.isChecked():
                self.ui.PLL1PFDFreq.setText("0.00")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL1RDividerSetting"):
                r_div = max(1, self.ui.PLL1RDividerSetting.value())

            # 计算PFD频率
            if r_div > 0:
                pll1_pfd_freq = fin_freq / r_div
                self.ui.PLL1PFDFreq.setText(f"{pll1_pfd_freq:.5f}")
                logger.debug(f"PLL1 PFD频率: {pll1_pfd_freq:.5f} MHz (Fin: {fin_freq}, R: {r_div})")

                # 通知其他窗口PLL1PFDFreq值已更新
                self._notify_pll1_pfd_freq_changed(pll1_pfd_freq)

                return pll1_pfd_freq
            else:
                self.ui.PLL1PFDFreq.setText("0.00")
                logger.warning("PLL1 R Divider 为0，无法计算PFD频率")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL1输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("Error")
            self._notify_pll1_pfd_freq_changed(0.0)
            return 0.0

    def _notify_pll1_pfd_freq_changed(self, pfd_freq):
        """通知其他窗口PLL1PFDFreq值已更新，并缓存该值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 缓存PLL1PFDFreq值
            if hasattr(bus, 'cache_pll1_pfd_freq'):
                bus.cache_pll1_pfd_freq(pfd_freq)
                logger.debug(f"已缓存PLL1PFDFreq值: {pfd_freq} MHz")

            # 发送PLL1PFDFreq更新信号
            if hasattr(bus, 'pll1_pfd_freq_updated'):
                bus.pll1_pfd_freq_updated.emit(pfd_freq)
                logger.debug(f"已发送PLL1PFDFreq更新信号: {pfd_freq} MHz")
            else:
                # 如果信号不存在，我们需要添加它
                logger.warning("RegisterUpdateBus中缺少pll1_pfd_freq_updated信号")

        except Exception as e:
            logger.error(f"通知PLL1PFDFreq更新时发生错误: {str(e)}")

    def _notify_vco_dist_freq_changed(self, vco_dist_freq):
        """通知其他窗口VCODistFreq值已更新，并缓存该值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 添加调试信息：验证VCODistFreq值的合理性
            logger.info(f"【PLL窗口】准备发送VCODistFreq更新: {vco_dist_freq} MHz")

            # 使用防重复错误警告机制
            self._check_vco_dist_freq_validity(vco_dist_freq)

            # 缓存VCODistFreq值
            if hasattr(bus, 'cache_vco_dist_freq'):
                bus.cache_vco_dist_freq(vco_dist_freq)
                logger.info(f"【PLL窗口】已缓存VCODistFreq值: {vco_dist_freq} MHz")

            # 发送VCODistFreq更新信号
            if hasattr(bus, 'vco_dist_freq_updated'):
                bus.vco_dist_freq_updated.emit(vco_dist_freq)
                logger.info(f"【PLL窗口】已发送VCODistFreq更新信号: {vco_dist_freq} MHz")
            else:
                # 如果信号不存在，记录警告
                logger.warning("RegisterUpdateBus中缺少vco_dist_freq_updated信号")

            # VCODistFreq变化后，只有在特定条件下才重新计算PLL1NDivider
            # 避免用户手动调整PLL2NDivider时触发不必要的PLL1NDivider计算
            should_recalc_pll1 = self._should_auto_calculate_pll1_ndivider()
            if should_recalc_pll1:
                logger.info("🔄 VCODistFreq变化，触发PLL1NDivider实时计算")
                QtCore.QTimer.singleShot(100, self._calculate_pll1_n_divider_realtime)
            else:
                logger.info("ℹ️ VCODistFreq变化，但跳过PLL1NDivider自动计算（用户手动调整模式）")

            # 触发频率范围验证（延迟稍长一些，避免过于频繁）
            QtCore.QTimer.singleShot(200, self._validate_vco_dist_freq_range)

        except Exception as e:
            logger.error(f"通知VCODistFreq更新时发生错误: {str(e)}")

    def _should_auto_calculate_pll1_ndivider(self):
        """判断是否应该自动计算PLL1NDivider

        只有当PLL1的反馈输入依赖于PLL2输出时，才需要在PLL2NDivider变化时重新计算PLL1NDivider

        Returns:
            bool: True表示应该自动计算，False表示跳过自动计算
        """
        try:
            # 检查是否正在更新PLL2NDivider（用户手动调整）
            if getattr(self, '_updating_pll2_n_divider', False):
                logger.debug("正在更新PLL2NDivider，跳过PLL1NDivider自动计算")
                return False

            # 检查PLL1 NCLK MUX模式
            pll1_nclk_mux = self._get_pll1_nclk_mux_value()

            # 根据PLL1 NCLK MUX的选择判断是否需要自动计算：
            # 0: OSCin - PLL1反馈直接来自OSCin，不依赖PLL2，无需重新计算
            # 1: Feedback Mux - PLL1反馈来自时钟输出，可能依赖VCODistFreq，需要重新计算
            # 2: PLL2 Prescaler - PLL1反馈直接来自PLL2 Prescaler，依赖PLL2，需要重新计算
            if pll1_nclk_mux == 0:
                logger.debug("PLL1使用OSCin作为反馈源，不依赖PLL2输出，无需重新计算PLL1NDivider")
                return False
            elif pll1_nclk_mux == 1:
                logger.debug("PLL1使用Feedback Mux作为反馈源，可能依赖VCODistFreq，需要重新计算PLL1NDivider")
                return True
            elif pll1_nclk_mux == 2:
                logger.debug("PLL1使用PLL2 Prescaler作为反馈源，依赖PLL2输出，需要重新计算PLL1NDivider")
                return True
            else:
                logger.warning(f"未知的PLL1 NCLK MUX值: {pll1_nclk_mux}，默认不重新计算")
                return False

        except Exception as e:
            logger.error(f"判断是否自动计算PLL1NDivider时出错: {str(e)}")
            # 出错时默认不自动计算，避免干扰用户操作
            return False

    def _cache_pll2_pfd_frequency(self, pll2_pfd_freq):
        """缓存PLL2PFD频率值，供同步系统参考窗口使用

        Args:
            pll2_pfd_freq (float): PLL2PFD频率值
        """
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 缓存PLL2PFD频率值
            if hasattr(bus, 'cache_pll2_pfd_freq'):
                bus.cache_pll2_pfd_freq(pll2_pfd_freq)
                logger.info(f"【PLL窗口】已缓存PLL2PFD频率: {pll2_pfd_freq:.3f} MHz，供同步系统参考窗口使用")

                # 触发系统参考界面重新计算InternalVCO
                self._trigger_sysref_window_recalculation()
            else:
                logger.warning("RegisterUpdateBus中缺少cache_pll2_pfd_freq方法")

        except Exception as e:
            logger.error(f"缓存PLL2PFD频率时发生错误: {str(e)}")

    def _cache_initial_pll2_pfd_frequency(self):
        """在窗口初始化时缓存当前PLL2PFD频率值

        这个方法在窗口初始化完成后调用，确保同步系统参考窗口能够获取到PLL2PFD频率
        """
        try:
            # 检查PLL2PFDFreq控件是否存在且有值
            if hasattr(self.ui, 'PLL2PFDFreq'):
                pfd_text = self.ui.PLL2PFDFreq.text()
                if pfd_text and pfd_text.strip() and pfd_text != "0.00":
                    try:
                        pll2_pfd_freq = float(pfd_text)
                        if pll2_pfd_freq > 0:
                            # 缓存这个值
                            self._cache_pll2_pfd_frequency(pll2_pfd_freq)
                            logger.info(f"【PLL窗口初始化】已缓存初始PLL2PFD频率: {pll2_pfd_freq:.3f} MHz")
                            return
                    except ValueError:
                        logger.debug(f"PLL2PFDFreq值无法转换为数字: '{pfd_text}'")

            # 如果控件没有值，尝试计算一个初始值
            logger.info("【PLL窗口初始化】PLL2PFDFreq控件无值，尝试计算初始值...")

            # 获取当前输入频率
            input_freq = 0.0
            if hasattr(self.ui, 'FreFin'):
                fin_text = self.ui.FreFin.text()
                try:
                    input_freq = float(fin_text) if fin_text else 0.0
                except ValueError:
                    input_freq = 0.0

            if input_freq > 0:
                # 计算PLL2PFD频率
                pll2_pfd_freq = self._calculate_pll2_pfd_frequency(input_freq)
                if pll2_pfd_freq > 0:
                    # 设置控件值并缓存
                    self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
                    self._cache_pll2_pfd_frequency(pll2_pfd_freq)
                    logger.info(f"【PLL窗口初始化】计算并缓存初始PLL2PFD频率: {pll2_pfd_freq:.3f} MHz")
                else:
                    logger.debug("【PLL窗口初始化】计算的PLL2PFD频率为0，跳过缓存")
            else:
                logger.debug("【PLL窗口初始化】输入频率为0，跳过PLL2PFD频率计算")

        except Exception as e:
            logger.error(f"缓存初始PLL2PFD频率时发生错误: {str(e)}")

    def _cache_pll2_n_divider(self, n_divider_value):
        """缓存PLL2NDivider值，供同步系统参考窗口使用

        Args:
            n_divider_value (float): PLL2NDivider值
        """
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 缓存PLL2NDivider值
            if hasattr(bus, 'cache_pll2_n_divider'):
                bus.cache_pll2_n_divider(n_divider_value)
                logger.info(f"【PLL窗口】已缓存PLL2NDivider值: {n_divider_value}，供同步系统参考窗口使用")
            else:
                logger.warning("RegisterUpdateBus中缺少cache_pll2_n_divider方法")

        except Exception as e:
            logger.error(f"缓存PLL2NDivider值时发生错误: {str(e)}")

    def _trigger_sysref_window_recalculation(self):
        """触发系统参考界面重新计算InternalVCO

        当PLL界面打开时，需要确保系统参考界面的InternalVCO值是正确的
        """
        try:
            logger.info("【PLL窗口初始化】触发系统参考界面重新计算InternalVCO...")

            # 尝试获取主窗口和系统参考界面
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'sync_sysref_window') and main_window.sync_sysref_window:
                sync_window = main_window.sync_sysref_window

                # 检查系统参考界面是否有重新计算方法
                if hasattr(sync_window, 'calculate_internal_vco_freq_from_pll2pfd'):
                    logger.info("【PLL窗口初始化】找到系统参考界面，触发InternalVCO重新计算")
                    sync_window.calculate_internal_vco_freq_from_pll2pfd()
                    logger.info("【PLL窗口初始化】系统参考界面InternalVCO重新计算完成")
                else:
                    logger.warning("【PLL窗口初始化】系统参考界面没有calculate_internal_vco_freq_from_pll2pfd方法")
            else:
                logger.info("【PLL窗口初始化】系统参考界面未打开，跳过InternalVCO重新计算")

        except Exception as e:
            logger.error(f"触发系统参考界面重新计算时出错: {str(e)}")

    def _ensure_pll2_pfd_calculated(self):
        """确保PLL2PFD已经计算

        在PLL2Cin初始化时调用，确保PLL2PFD控件有值
        """
        try:
            logger.info("【确保PLL2PFD】检查PLL2PFD是否已计算...")

            # 检查PLL2PFD控件是否有值
            if hasattr(self.ui, 'PLL2PFDFreq'):
                current_value = self.ui.PLL2PFDFreq.text()
                logger.info(f"【确保PLL2PFD】当前PLL2PFD值: '{current_value}'")

                if current_value and current_value.strip():
                    logger.info(f"【确保PLL2PFD】✅ PLL2PFD已有值: {current_value} MHz")
                    return
                else:
                    logger.info("【确保PLL2PFD】PLL2PFD值为空，需要计算")
            else:
                logger.warning("【确保PLL2PFD】PLL2PFD控件不存在")
                return

            # 如果PLL2PFD为空，触发频率计算
            logger.info("【确保PLL2PFD】触发PLL频率计算...")

            # 获取输入频率
            oscin_freq = 0.0
            if hasattr(self.ui, "OSCinFreq"):
                oscin_text = self.ui.OSCinFreq.text()
                if oscin_text and oscin_text.strip():
                    try:
                        oscin_freq = float(oscin_text)
                        logger.info(f"【确保PLL2PFD】OSCin频率: {oscin_freq} MHz")
                    except ValueError:
                        logger.warning(f"【确保PLL2PFD】OSCin频率无法转换: '{oscin_text}'")
                        oscin_freq = 48.0  # 使用默认值
                else:
                    logger.info("【确保PLL2PFD】OSCin频率为空，使用默认值")
                    oscin_freq = 48.0  # 使用默认值

            if oscin_freq > 0:
                # 直接计算PLL2PFD
                pll2_pfd_freq = self._calculate_pll2_output_with_source(oscin_freq)
                logger.info(f"【确保PLL2PFD】✅ 计算得到PLL2PFD: {pll2_pfd_freq:.3f} MHz")

                # 同时缓存PLL2NDivider值
                if hasattr(self.ui, "PLL2NDivider"):
                    current_n_divider = self.ui.PLL2NDivider.value()
                    self._cache_pll2_n_divider(current_n_divider)
                    logger.info(f"【确保PLL2PFD】同时缓存PLL2NDivider: {current_n_divider}")
            else:
                logger.warning("【确保PLL2PFD】❌ 无法获取有效的输入频率")

        except Exception as e:
            logger.error(f"确保PLL2PFD计算时出错: {str(e)}")

    def on_internal_vco_freq_updated(self, internal_vco_freq):
        """处理InternalVCOFreq更新事件，同步到VCODistFreq

        Args:
            internal_vco_freq: 从同步系统参考窗口计算出的InternalVCOFreq值(MHz)
        """
        try:
            logger.info(f"【VCODistFreq同步】收到InternalVCOFreq更新: {internal_vco_freq} MHz")

            # 更新VCODistFreq控件的值
            if hasattr(self.ui, "VCODistFreq"):
                # 防止递归调用的标志
                if hasattr(self, '_updating_vco_dist_freq') and self._updating_vco_dist_freq:
                    return

                self._updating_vco_dist_freq = True
                try:
                    # 格式化为5位小数的字符串
                    formatted_freq = f"{internal_vco_freq:.5f}"
                    old_value = self.ui.VCODistFreq.text()

                    # 只有当值不同时才更新
                    if old_value != formatted_freq:
                        self.ui.VCODistFreq.setText(formatted_freq)
                        logger.info(f"【VCODistFreq同步】VCODistFreq更新: '{old_value}' -> '{formatted_freq}' MHz (来自InternalVCOFreq)")

                        # ⚠️ 不要调用_notify_vco_dist_freq_changed，避免循环同步
                        # 这是来自同步系统参考窗口的更新，只更新UI显示即可
                        logger.info("【VCODistFreq同步】跳过信号通知，避免循环同步")
                    else:
                        logger.debug("【VCODistFreq同步】VCODistFreq值未变化，跳过更新")

                finally:
                    self._updating_vco_dist_freq = False
            else:
                logger.warning("【VCODistFreq同步】VCODistFreq控件不存在")

        except Exception as e:
            logger.error(f"处理InternalVCOFreq更新时出错: {str(e)}")



    def _calculate_fin0_output(self, pll2_pfd_freq):
        """计算Fin0的输出频率"""
        try:
            if not hasattr(self.ui, "Fin0Freq"):
                return

            # 检查当前VCO模式是否为Fin0模式
            vco_mode = 0
            if hasattr(self.ui, "comboVcoMode"):
                if hasattr(self.ui.comboVcoMode, 'currentData'):
                    vco_mode = self.ui.comboVcoMode.currentData() or 0
                else:
                    vco_mode = self.ui.comboVcoMode.currentIndex()

            if vco_mode == 3:  # Fin0模式
                # 在Fin0模式下，Fin0Freq是用户输入控件，不应该被自动计算覆盖
                logger.debug("当前为Fin0模式，跳过Fin0Freq自动计算（保持用户输入值）")
                return

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.Fin0Freq.setText("0.00")
                return

            # 获取N分频器值
            n_div = 1
            if hasattr(self.ui, "PLL2NDivider"):
                n_div = max(1, self.ui.PLL2NDivider.value())

            # 获取预分频器值
            prescaler_val = 1.0
            if hasattr(self.ui, "PLL2Prescaler"):
                try:
                    prescaler_text = self.ui.PLL2Prescaler.currentText()
                    prescaler_val = float(prescaler_text) if prescaler_text else 1.0
                except ValueError:
                    logger.error(f"无法将PLL2Prescaler值 '{prescaler_text}' 转换为数字")
                    prescaler_val = 1.0

            # 计算VCO频率和Fin0频率
            if prescaler_val > 0:
                vco2_freq = pll2_pfd_freq * n_div
                fin0_freq = vco2_freq * prescaler_val
                self.ui.Fin0Freq.setText(f"{fin0_freq:.5f}")
                logger.debug(f"Fin0频率: {fin0_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_div}, Prescaler: {prescaler_val})")
            else:
                logger.warning("PLL2 Prescaler 值为0或无效，无法计算Fin0频率")
                self.ui.Fin0Freq.setText("0.00")

        except Exception as e:
            logger.error(f"计算Fin0输出时出错: {str(e)}")
            if hasattr(self.ui, "Fin0Freq"):
                self.ui.Fin0Freq.setText("Error")

    def _calculate_vco_dist_freq(self, pll2_pfd_freq):
        """计算VCODistFreq频率

        VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler

        Args:
            pll2_pfd_freq: PLL2的PFD频率
        """
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                return

            # 检查VCO模式，如果是CLKin1或Fin0模式，使用对应的固定频率值
            if hasattr(self.ui, "comboVcoMode"):
                vco_mode = self.ui.comboVcoMode.currentIndex()
                if vco_mode == 2:  # CLKin1模式
                    clkin1_freq = self.clkin_frequencies.get("ClkIn1", 0.0)
                    if clkin1_freq > 0:
                        self.ui.VCODistFreq.setText(f"{clkin1_freq:.5f}")
                        logger.info(f"VCO模式CLKin1: 显示CLKin1频率 {clkin1_freq:.5f} MHz")
                        self._notify_vco_dist_freq_changed(clkin1_freq)
                        return
                elif vco_mode == 3:  # Fin0模式
                    # 使用新的Fin0模式计算逻辑
                    logger.info("VCO模式Fin0: 使用新的计算逻辑")
                    self._update_vco_dist_freq_for_fin0_mode()
                    return
                # VCO0和VCO1模式继续执行正常的PLL计算

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.VCODistFreq.setText("0.00")
                return

            # 获取PLL2NDivider值
            n_div = 1
            if hasattr(self.ui, "PLL2NDivider"):
                n_div = max(1, self.ui.PLL2NDivider.value())

            # 获取PLL2Prescaler值
            prescaler_val = 1.0
            if hasattr(self.ui, "PLL2Prescaler"):
                try:
                    prescaler_text = self.ui.PLL2Prescaler.currentText()
                    prescaler_val = float(prescaler_text) if prescaler_text else 1.0
                except ValueError:
                    logger.error(f"无法将PLL2Prescaler值 '{prescaler_text}' 转换为数字")
                    prescaler_val = 1.0

            # 根据PLL2NclkMux模式选择不同的计算公式
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()

            if pll2_nclk_mux == 0:
                # 模式0: PLL2 Prescaler模式
                # VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler
                if pll2_pfd_freq > 0 and n_div > 0 and prescaler_val > 0:
                    vco_dist_freq = pll2_pfd_freq * n_div * prescaler_val
                    self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                    logger.debug(f"VCODistFreq (Prescaler模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_div}, Prescaler: {prescaler_val})")
                    self._notify_vco_dist_freq_changed(vco_dist_freq)
                else:
                    self.ui.VCODistFreq.setText("0.00")
                    logger.warning(f"VCODistFreq计算参数无效 (Prescaler模式): PFD={pll2_pfd_freq}, N={n_div}, Prescaler={prescaler_val}")
                    self._notify_vco_dist_freq_changed(0.0)

            elif pll2_nclk_mux == 1:
                # 模式1: Feedback Mux模式
                # VCODistFreq = PLL2PFDFreq × PLL2NDivider × 反馈分频比
                # 获取FB_MUX值
                fb_mux_value = 0
                if hasattr(self.ui, "FBMUX"):
                    if hasattr(self.ui.FBMUX, 'currentData'):
                        fb_mux_value = self.ui.FBMUX.currentData() or 0
                    else:
                        fb_mux_value = self.ui.FBMUX.currentIndex()

                feedback_divider = 1.0

                if fb_mux_value == 0:  # CLKout6
                    feedback_divider = self._get_dclk6_7div_value()
                    logger.debug(f"反馈模式使用CLKout6，分频比: {feedback_divider}")
                elif fb_mux_value == 1:  # CLKout8
                    feedback_divider = self._get_dclk8_9div_value()
                    logger.debug(f"反馈模式使用CLKout8，分频比: {feedback_divider}")
                elif fb_mux_value == 2:  # SYSREF Divider
                    feedback_divider = self._get_sysref_divider_value()
                    logger.debug(f"反馈模式使用SYSREF Divider，分频比: {feedback_divider}")
                else:
                    logger.warning(f"反馈模式下未知的FB_MUX值: {fb_mux_value}")
                    feedback_divider = 1.0

                if pll2_pfd_freq > 0 and n_div > 0 and feedback_divider > 0:
                    vco_dist_freq = pll2_pfd_freq * n_div * feedback_divider
                    self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
                    logger.debug(f"VCODistFreq (Feedback模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_div}, 反馈分频: {feedback_divider})")
                    self._notify_vco_dist_freq_changed(vco_dist_freq)
                else:
                    self.ui.VCODistFreq.setText("0.00")
                    logger.warning(f"VCODistFreq计算参数无效 (Feedback模式): PFD={pll2_pfd_freq}, N={n_div}, 反馈分频={feedback_divider}")
                    self._notify_vco_dist_freq_changed(0.0)
            else:
                logger.warning(f"未知的PLL2NclkMux值: {pll2_nclk_mux}")
                self.ui.VCODistFreq.setText("0.00")
                self._notify_vco_dist_freq_changed(0.0)

        except Exception as e:
            logger.error(f"计算VCODistFreq时出错: {str(e)}")
            if hasattr(self.ui, "VCODistFreq"):
                self.ui.VCODistFreq.setText("Error")

    def _get_float_from_lineedit(self, line_edit, default_value=0.0):
        """安全地从QLineEdit获取浮点数"""
        try:
            text = line_edit.text()
            return float(text) if text else default_value
        except (ValueError, AttributeError):
            return default_value

    def _get_float_from_spinbox(self, spinbox, default_value=0.0):
        """安全地从QSpinBox获取浮点数"""
        try:
            return float(spinbox.value()) if spinbox else default_value
        except (ValueError, AttributeError):
            return default_value

    def _set_float_to_spinbox(self, spinbox, value):
        """安全地设置QSpinBox的值"""
        try:
            if spinbox:
                spinbox.setValue(int(round(value)))
        except (ValueError, AttributeError) as e:
            logger.error(f"设置SpinBox值时出错: {str(e)}")



    def _handle_pll1_power_down(self):
        """处理PLL1电源控制"""
        try:
            pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD") if self.register_manager else 0
            logger.info(f"PLL1电源状态: {'关闭' if pll1_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL1电源控制时出错: {str(e)}")

    def _handle_pll2_power_down(self):
        """处理PLL2电源控制"""
        try:
            pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD") if self.register_manager else 0
            logger.info(f"PLL2电源状态: {'关闭' if pll2_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL2电源控制时出错: {str(e)}")

    def _handle_pll1_reset(self):
        """处理PLL1复位"""
        try:
            logger.info("PLL1复位操作")
            # 可以在这里添加复位后的特殊处理
        except Exception as e:
            logger.error(f"处理PLL1复位时出错: {str(e)}")

    def _handle_divider_change(self, widget_name):
        """处理分频器值变化"""
        try:
            if widget_name == "PLL2NDivider":
                current_value = self.ui.PLL2NDivider.value() if hasattr(self.ui, "PLL2NDivider") else "N/A"
                logger.info(f"🔄 PLL2NDivider手动修改为: {current_value}")

                # 设置标志，表示正在手动调整PLL2NDivider
                self._updating_pll2_n_divider = True

                try:
                    # 缓存PLL2NDivider值，供同步系统参考窗口使用
                    if hasattr(self.ui, "PLL2NDivider"):
                        self._cache_pll2_n_divider(self.ui.PLL2NDivider.value())

                    # 检查是否在反馈模式下
                    pll2_nclk_mux = self._get_pll2_nclk_mux_value()
                    if pll2_nclk_mux == 1:
                        logger.info("⚠️ 当前在反馈模式下，手动修改PLL2NDivider可能会被自动调整覆盖")
                    else:
                        logger.info("✅ 当前在Prescaler模式下，手动修改PLL2NDivider将生效")

                    # 重新计算频率
                    self.calculate_output_frequencies()

                finally:
                    # 确保标志被重置
                    self._updating_pll2_n_divider = False

            else:
                logger.info(f"分频器 {widget_name} 值变化")
                # 重新计算频率
                self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"处理分频器变化时出错: {str(e)}")
            # 确保在异常情况下也重置标志
            if widget_name == "PLL2NDivider":
                self._updating_pll2_n_divider = False

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前PLL状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取PLL状态
                status["pll1_enabled"] = not self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                status["pll2_enabled"] = not self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                status["vco_enabled"] = not self.register_manager.get_bit_field_value("0x50", "VCO_PD")

                # 获取频率信息
                status["current_clock_source"] = self.current_clock_source
                status["clkin_frequencies"] = self.clkin_frequencies.copy()

            return status

        except Exception as e:
            logger.error(f"获取PLL状态时出错: {str(e)}")
            return {}

    def update_clock_source(self, source_name, frequency, divider):
        """更新时钟源配置

        Args:
            source_name: 时钟源名称
            frequency: 频率值
            divider: 分频值
        """
        try:
            # 规范化时钟源名称
            normalized_source = self._normalize_clock_source_name(source_name)

            # 更新当前时钟源
            self.current_clock_source = normalized_source

            # 更新频率和分频值
            self.clkin_frequencies[normalized_source] = frequency
            self.clkin_divider_values[normalized_source] = divider

            # 更新UI显示
            self._update_clock_source_ui(frequency, divider)

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"已更新时钟源: {normalized_source}, 频率: {frequency}MHz, 分频: {divider}")

        except Exception as e:
            logger.error(f"更新时钟源时出错: {str(e)}")

    def _update_clock_source_ui(self, frequency, divider):
        """更新时钟源相关的UI控件"""
        if hasattr(self.ui, "FreFin"):
            self.ui.FreFin.setText(str(frequency))
        if hasattr(self.ui, "PLL1RDividerSetting"):
            self.ui.PLL1RDividerSetting.setValue(divider)

    def on_global_clock_source_selected(self, source_name, frequency, divider):
        """处理全局时钟源选择事件"""
        logger.info(f"【PLL工具窗口】收到时钟源选择事件: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")

        try:
            # 规范化时钟源名称
            normalized_source = self._normalize_clock_source_name(source_name)
            logger.info(f"【PLL工具窗口】规范化时钟源名称: {source_name} -> {normalized_source}")

            # 更新当前选中的时钟源
            self.current_clock_source = normalized_source

            # 更新存储的时钟源配置
            self.clkin_frequencies[normalized_source] = frequency
            self.clkin_divider_values[normalized_source] = divider
            logger.info(f"【PLL工具窗口】已更新时钟源 {normalized_source} 配置: 频率={frequency}MHz, 分频={divider}")

            # 更新UI显示
            self._update_clock_source_selection_ui(frequency, divider, normalized_source)

            # 重新计算输出频率
            self.calculate_output_frequencies()
            logger.info("【PLL工具窗口】已重新计算输出频率")

        except Exception as e:
            logger.error(f"【PLL工具窗口】处理时钟源选择事件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _update_clock_source_selection_ui(self, frequency, divider, normalized_source):
        """更新时钟源选择相关的UI"""
        # 更新FreFin控件
        if hasattr(self.ui, "FreFin"):
            old_value = self.ui.FreFin.text()
            self.ui.FreFin.setText(str(frequency))
            logger.info(f"【PLL工具窗口】FreFin更新: '{old_value}' -> '{frequency}'")
        else:
            logger.warning("【PLL工具窗口】FreFin控件不存在!")

        # 更新PLL1RDividerSetting控件
        if hasattr(self.ui, "PLL1RDividerSetting"):
            old_value = self.ui.PLL1RDividerSetting.value()

            # 优先从RegisterManager获取值
            reg_value = self._get_pll1r_divider_from_register(normalized_source)

            if reg_value is not None and 0 < reg_value <= 127:
                self.ui.PLL1RDividerSetting.setValue(reg_value)
                logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自RM): {old_value} -> {reg_value}")
            else:
                self.ui.PLL1RDividerSetting.setValue(divider)
                logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自事件): {old_value} -> {divider}")
        else:
            logger.warning("【PLL工具窗口】PLL1RDividerSetting控件不存在!")

    def _get_pll1r_divider_from_register(self, normalized_source):
        """从RegisterManager获取PLL1RDivider值"""
        if not self.register_manager:
            return None

        widget_map = self.widget_register_map.get("PLL1RDividerSetting", {})
        if normalized_source not in widget_map:
            return None

        try:
            reg_addr, bit_field = widget_map[normalized_source]
            reg_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
            logger.info(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider ({reg_addr}[{bit_field}]): {reg_value}")
            return reg_value
        except Exception as e:
            logger.warning(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider 值失败: {e}")
            return None

    def _get_current_mode(self):
        """获取当前工作模式

        优先使用缓存的模式，如果没有明确设置则返回默认的DualLoop

        Returns:
            str: 当前模式名称，默认为"DualLoop"
        """
        try:
            # 如果有缓存的模式，优先使用
            if hasattr(self, '_current_mode_cache') and self._current_mode_cache:
                logger.debug(f"使用缓存的模式: {self._current_mode_cache}")
                return self._current_mode_cache

            # 检查是否启用了模式化计算
            if not self._is_mode_based_calculation_enabled():
                logger.debug("未启用模式化计算，返回默认DualLoop模式")
                return "DualLoop"

            if not self.register_manager:
                logger.debug("RegisterManager不可用，返回默认DualLoop模式")
                return "DualLoop"

            # 只有在明确启用模式化计算时才进行寄存器检测
            logger.debug("进行寄存器模式检测")

            # 读取关键寄存器位来判断模式
            # 0x4F寄存器的关键位
            pll1_nclk_mux = self.register_manager.get_bit_field_value("0x4F", "PLL1_NCLK_MUX[1:0]") or 0
            pll2_nclk_mux = self.register_manager.get_bit_field_value("0x4F", "PLL2_NCLK_MUX") or 0
            fb_mux_en = self.register_manager.get_bit_field_value("0x4F", "FB_MUX_EN") or 0

            # 0x50寄存器的关键位
            pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD") or 0
            oscin_pd = self.register_manager.get_bit_field_value("0x50", "OSCin_PD") or 0
            vco_pd = self.register_manager.get_bit_field_value("0x50", "VCO_PD") or 0

            # 0x83寄存器的关键位
            pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD") or 0

            # 根据寄存器位组合判断模式
            if oscin_pd == 1 and vco_pd == 1 and pll1_pd == 1 and pll2_pd == 1:
                detected_mode = "DistributionFin1"
            elif pll1_pd == 1:
                if pll2_nclk_mux == 1 and fb_mux_en == 1:
                    detected_mode = "SingleLoop0Dealy"
                else:
                    detected_mode = "SingleLoop"
            elif pll1_nclk_mux == 0b10:  # PLL1使用PLL2 Prescaler
                if pll2_nclk_mux == 1:
                    detected_mode = "DualLoop0DealyNestedandCasc"
                else:
                    detected_mode = "DualLoop0DealyNested"
            elif pll2_nclk_mux == 1 and fb_mux_en == 1:
                detected_mode = "DualLoop0DealyCascaded"
            else:
                detected_mode = "DualLoop"

            logger.debug(f"寄存器检测到的模式: {detected_mode}")

            # 缓存检测到的模式
            self._current_mode_cache = detected_mode
            return detected_mode

        except Exception as e:
            logger.error(f"获取当前模式时出错: {str(e)}")
            return "DualLoop"  # 出错时返回默认模式

    def on_mode_changed(self, mode_name):
        """处理模式变化信号"""
        logger.info(f"ModernPLLHandler 收到模式变化信号: {mode_name}")
        # 缓存当前模式
        self._current_mode_cache = mode_name
        # 模式变化后重新计算频率
        self.calculate_output_frequencies()

    def set_pll_preset(self, preset_name):
        """设置PLL预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用PLL预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL1RDividerSetting": 1,
                    "PLL2RDivider": 1,
                    "PLL2NDivider": 100
                },
                "low_power": {
                    "PLL1_PD": 1,
                    "PLL2_PD": 1,
                    "VCO_PD": 1
                },
                "high_performance": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL2NDivider": 200
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            for param_name, value in preset.items():
                if param_name.endswith("_PD"):
                    # 电源控制位
                    bit_name = param_name
                    reg_addr = "0x50" if param_name.startswith("PLL1") or param_name.startswith("VCO") else "0x83"
                    if self.register_manager:
                        self.register_manager.set_bit_field_value(reg_addr, bit_name, value)
                elif hasattr(self.ui, param_name):
                    # UI控件值
                    widget = getattr(self.ui, param_name)
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)

            # 重新计算频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"设置PLL预设时出错: {str(e)}")



    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os

            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)

            register_manager = RegisterManager(registers_config)

            # 创建实例
            instance = cls(parent, register_manager)

            # 创建模拟主窗口用于测试
            class MockMainWindow:
                def __init__(self):
                    self.auto_write_mode = False
                    self.register_manager = register_manager
                    # 模拟寄存器服务
                    self.register_service = None

                def on_register_selected(self, reg_addr):
                    """模拟寄存器选择方法"""
                    logger.debug(f"模拟主窗口: 寄存器选择 {reg_addr}")

            # 设置模拟主窗口引用
            instance.main_window = MockMainWindow()
            logger.info("已为测试实例设置模拟主窗口引用")

            logger.info("创建现代化PLLHandler测试实例成功")
            return instance

        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise

    def _log_clk_output_window_warning(self):
        """记录时钟输出窗口警告，带防重复机制"""
        import time
        current_time = time.time()

        # 检查是否需要显示警告（防止过于频繁）
        if current_time - self._last_clk_output_warning_time > self._clk_output_warning_interval:
            logger.warning("❌ 时钟输出窗口未打开或不存在 (clk_output_window)")
            logger.info("💡 提示：打开时钟输出窗口可以获得更准确的频率计算结果")
            self._last_clk_output_warning_time = current_time
        else:
            # 使用debug级别记录，避免过多警告
            logger.debug("时钟输出窗口未打开（已抑制重复警告）")

    def _log_clk_output_connection_warning(self):
        """记录时钟输出窗口连接警告，带防重复机制"""
        import time
        current_time = time.time()

        # 检查是否需要显示警告（防止过于频繁）
        if current_time - self._last_clk_output_connection_warning_time > self._clk_output_warning_interval:
            logger.warning("⚠️ 时钟输出窗口未打开，将在需要时动态连接")
            logger.info("💡 提示：打开时钟输出窗口可以实现实时频率同步")
            self._last_clk_output_connection_warning_time = current_time
        else:
            # 使用debug级别记录，避免过多警告
            logger.debug("时钟输出窗口连接延迟（已抑制重复警告）")

    def _check_vco_dist_freq_validity(self, vco_dist_freq):
        """检查VCODistFreq值的有效性，带防重复错误警告机制"""
        import time
        current_time = time.time()

        if vco_dist_freq > 10000:  # 大于10GHz
            if current_time - self._last_vco_dist_freq_error_time > self._clk_output_warning_interval:
                logger.error(f"【PLL窗口】VCODistFreq值异常大: {vco_dist_freq} MHz，可能存在计算错误")
                logger.info("💡 提示：检查PLL参数设置是否正确")

                # 添加详细的诊断信息
                self._log_vco_dist_freq_diagnostic_info()

                self._last_vco_dist_freq_error_time = current_time
            else:
                logger.debug(f"VCODistFreq值异常大: {vco_dist_freq} MHz（已抑制重复错误）")
        elif vco_dist_freq < 0.1:  # 小于0.1MHz
            if current_time - self._last_vco_dist_freq_error_time > self._clk_output_warning_interval:
                logger.error(f"【PLL窗口】VCODistFreq值异常小: {vco_dist_freq} MHz，可能存在计算错误")
                logger.info("💡 提示：检查时钟源配置和PLL参数设置")
                self._last_vco_dist_freq_error_time = current_time
            else:
                logger.debug(f"VCODistFreq值异常小: {vco_dist_freq} MHz（已抑制重复错误）")
        else:
            logger.info(f"【PLL窗口】VCODistFreq值正常: {vco_dist_freq} MHz")

    def _log_vco_dist_freq_diagnostic_info(self):
        """记录VCODistFreq异常时的诊断信息"""
        try:
            logger.error("=" * 60)
            logger.error("【VCODistFreq诊断】开始诊断异常大值的原因")
            logger.error("=" * 60)

            # 1. 检查基础参数
            if hasattr(self.ui, "PLL2PFDFreq"):
                pll2_pfd_text = self.ui.PLL2PFDFreq.text()
                logger.error(f"【诊断】PLL2PFDFreq: '{pll2_pfd_text}'")

            # 2. 检查PLL2NDivider
            n_divider = self._get_pll2_n_divider_value()
            logger.error(f"【诊断】PLL2NDivider: {n_divider}")

            # 3. 检查PLL2Prescaler
            prescaler_val = self._get_pll2_prescaler_value()
            logger.error(f"【诊断】PLL2Prescaler: {prescaler_val}")

            # 4. 检查PLL2NclkMux模式
            pll2_nclk_mux = self._get_pll2_nclk_mux_value()
            logger.error(f"【诊断】PLL2NclkMux模式: {pll2_nclk_mux}")

            # 5. 检查FBMUX值（如果是Feedback模式）
            if pll2_nclk_mux == 1:
                fb_mux_value = self._get_fb_mux_value()
                logger.error(f"【诊断】FBMUX值: {fb_mux_value}")

                if fb_mux_value == 2:  # SYSREF Divider
                    sysref_div = self._get_sysref_divider_value()
                    logger.error(f"【诊断】SYSREF分频器值: {sysref_div}")

                    # 检查SYSREF分频器是否异常大
                    if sysref_div > 10000:
                        logger.error(f"【诊断】⚠️ SYSREF分频器值异常大: {sysref_div}")
                        logger.error("【诊断】💡 这可能是导致VCODistFreq异常大的原因")

                        # 尝试从RegisterManager获取原始寄存器值
                        if hasattr(self, 'register_manager') and self.register_manager:
                            try:
                                reg_value = self.register_manager.get_register_value("0x4A")
                                if reg_value is not None:
                                    sysref_div_raw = reg_value & 0x1FFF
                                    logger.error(f"【诊断】寄存器0x4A原始值: 0x{reg_value:04X}")
                                    logger.error(f"【诊断】SYSREF_DIV[12:0]提取值: {sysref_div_raw}")
                            except Exception as e:
                                logger.error(f"【诊断】获取寄存器值失败: {str(e)}")

                elif fb_mux_value in [0, 1]:  # CLKout6/CLKout8
                    if fb_mux_value == 0:
                        feedback_divider = self._get_dclk6_7div_value()
                        logger.error(f"【诊断】CLKout6分频器值: {feedback_divider}")
                    else:
                        feedback_divider = self._get_dclk8_9div_value()
                        logger.error(f"【诊断】CLKout8分频器值: {feedback_divider}")

            # 6. 检查PLL2Cin频率（如果是Feedback模式）
            if pll2_nclk_mux == 1:
                pll2_cin_freq = self._get_pll2_cin_frequency()
                logger.error(f"【诊断】PLL2Cin频率: {pll2_cin_freq}")

            # 7. 计算预期的VCODistFreq值
            try:
                pll2_pfd_freq = float(self.ui.PLL2PFDFreq.text()) if hasattr(self.ui, "PLL2PFDFreq") and self.ui.PLL2PFDFreq.text() else 0.0

                if pll2_nclk_mux == 0:  # Prescaler模式
                    expected_vco = pll2_pfd_freq * n_divider * prescaler_val
                    logger.error(f"【诊断】预期VCODistFreq (Prescaler模式): {pll2_pfd_freq} × {n_divider} × {prescaler_val} = {expected_vco:.2f} MHz")
                elif pll2_nclk_mux == 1:  # Feedback模式
                    if fb_mux_value == 2:  # SYSREF Divider
                        expected_vco = pll2_pfd_freq * n_divider * sysref_div
                        logger.error(f"【诊断】预期VCODistFreq (SYSREF模式): {pll2_pfd_freq} × {n_divider} × {sysref_div} = {expected_vco:.2f} MHz")
                    else:
                        expected_vco = pll2_cin_freq * n_divider
                        logger.error(f"【诊断】预期VCODistFreq (Feedback模式): {pll2_cin_freq} × {n_divider} = {expected_vco:.2f} MHz")
            except Exception as e:
                logger.error(f"【诊断】计算预期VCODistFreq时出错: {str(e)}")

            # 8. 检查当前VCODistFreq控件的实际值
            try:
                if hasattr(self.ui, "VCODistFreq"):
                    current_vco_text = self.ui.VCODistFreq.text()
                    logger.error(f"【诊断】当前VCODistFreq控件显示值: '{current_vco_text}'")

                    # 检查是否有缓存值
                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                    bus = RegisterUpdateBus.instance()
                    if hasattr(bus, 'get_cached_vco_dist_freq'):
                        cached_vco = bus.get_cached_vco_dist_freq()
                        logger.error(f"【诊断】缓存的VCODistFreq值: {cached_vco}")

                    # 检查是否有其他计算方法被调用
                    logger.error("【诊断】检查可能的计算路径...")

                    # 检查VCO模式
                    if hasattr(self.ui, "comboVcoMode"):
                        vco_mode = self.ui.comboVcoMode.currentIndex()
                        vco_mode_text = self.ui.comboVcoMode.currentText()
                        logger.error(f"【诊断】当前VCO模式: {vco_mode} ({vco_mode_text})")

                        if vco_mode == 3:  # Fin0模式
                            if hasattr(self.ui, "Fin0Freq"):
                                fin0_freq_text = self.ui.Fin0Freq.text()
                                logger.error(f"【诊断】Fin0Freq值: '{fin0_freq_text}'")

                                # 检查Fin0相关控件状态
                                div2_checked = self.ui.Div2.isChecked() if hasattr(self.ui, "Div2") else False
                                fin0_pd_checked = self.ui.Fin0PD.isChecked() if hasattr(self.ui, "Fin0PD") else False
                                logger.error(f"【诊断】Div2选中: {div2_checked}, Fin0PD选中: {fin0_pd_checked}")

                                # 如果是Fin0模式，这可能解释异常值
                                if fin0_freq_text and float(fin0_freq_text) > 3000:
                                    logger.error("【诊断】⚠️ Fin0Freq值异常大，可能是问题源头!")

            except Exception as e:
                logger.error(f"【诊断】检查当前VCODistFreq状态时出错: {str(e)}")

            logger.error("=" * 60)
            logger.error("【VCODistFreq诊断】诊断完成")
            logger.error("=" * 60)

        except Exception as e:
            logger.error(f"【诊断】诊断过程中出错: {str(e)}")

    def _restore_lineedit_values_from_cache(self):
        """从缓存恢复LineEdit控件的值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 定义需要缓存的lineEdit控件（只包含可编辑的控件）
            lineedit_controls = [
                "OSCinFreq",
                "ExternalVCXOFreq",
                "FreFin",
                "Fin0Freq"  # Fin0Freq现在是输入控件，需要缓存
                # 注意：VCODistFreq, PLL1PFDFreq, PLL2PFDFreq 是计算结果，不需要缓存
            ]

            restored_count = 0
            for control_name in lineedit_controls:
                if hasattr(self.ui, control_name):
                    # 从缓存获取值
                    cached_value = bus.get_cached_lineedit_value(self._window_name, control_name)
                    if cached_value is not None:
                        control = getattr(self.ui, control_name)
                        # 阻塞信号以避免触发不必要的事件
                        control.blockSignals(True)
                        control.setText(cached_value)
                        control.blockSignals(False)

                        restored_count += 1
                        logger.info(f"从缓存恢复LineEdit值: {control_name} = '{cached_value}'")
                    else:
                        logger.debug(f"没有缓存的值: {control_name}")
                else:
                    logger.warning(f"未找到控件: {control_name}")

            if restored_count > 0:
                logger.info(f"成功从缓存恢复了 {restored_count} 个LineEdit控件的值")
            else:
                logger.debug("没有从缓存恢复任何LineEdit控件的值")

        except Exception as e:
            logger.error(f"从缓存恢复LineEdit值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _cache_lineedit_value(self, control_name, value):
        """缓存单个LineEdit控件的值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            bus.cache_lineedit_value(self._window_name, control_name, value)
            logger.debug(f"已缓存LineEdit值: {control_name} = '{value}'")
        except Exception as e:
            logger.error(f"缓存LineEdit值时发生错误: {str(e)}")

    def _cache_all_lineedit_values(self):
        """缓存所有LineEdit控件的当前值"""
        try:
            lineedit_controls = [
                "OSCinFreq",
                "ExternalVCXOFreq",
                "FreFin"
            ]

            cached_count = 0
            for control_name in lineedit_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    current_value = control.text()
                    self._cache_lineedit_value(control_name, current_value)
                    cached_count += 1

            logger.debug(f"已缓存 {cached_count} 个LineEdit控件的值")

        except Exception as e:
            logger.error(f"缓存所有LineEdit值时发生错误: {str(e)}")

    def _connect_vco_mode_signals(self):
        """连接VCO模式控件的信号"""
        try:
            # 连接comboVcoMode控件
            if hasattr(self.ui, "comboVcoMode"):
                self.ui.comboVcoMode.currentIndexChanged.connect(self._on_vco_mode_changed)
                logger.info("已连接comboVcoMode信号")

            # 连接VCODistFreq控件的文本变化信号，用于实时验证频率范围
            if hasattr(self.ui, "VCODistFreq"):
                self.ui.VCODistFreq.textChanged.connect(self._validate_vco_dist_freq_range)
                logger.info("已连接VCODistFreq文本变化信号")

            logger.info("VCO模式控件信号连接完成")

        except Exception as e:
            logger.error(f"连接VCO模式控件信号时出错: {str(e)}")

    def _connect_fin0_signals(self):
        """连接Fin0相关控件的信号"""
        try:
            # 连接Fin0Freq控件的信号
            if hasattr(self.ui, "Fin0Freq"):
                self.ui.Fin0Freq.returnPressed.connect(self._on_fin0_freq_changed)
                self.ui.Fin0Freq.textChanged.connect(self._on_fin0_freq_changed)
                logger.info("已连接Fin0Freq信号")

            # 连接Div2控件的信号
            if hasattr(self.ui, "Div2"):
                self.ui.Div2.stateChanged.connect(self._on_fin0_div2_changed)
                logger.info("已连接Div2信号")

            # 连接Fin0PD控件的信号
            if hasattr(self.ui, "Fin0PD"):
                self.ui.Fin0PD.stateChanged.connect(self._on_fin0_pd_changed)
                logger.info("已连接Fin0PD信号")

            logger.info("Fin0相关控件信号连接完成")

        except Exception as e:
            logger.error(f"连接Fin0相关控件信号时出错: {str(e)}")

    def _on_fin0_freq_changed(self):
        """当Fin0Freq控件值改变时的处理函数"""
        try:
            logger.info("🔄 检测到Fin0Freq值变化")

            # 检查当前VCO模式是否为Fin0模式
            if hasattr(self.ui, "comboVcoMode"):
                vco_mode = 0
                if hasattr(self.ui.comboVcoMode, 'currentData'):
                    vco_mode = self.ui.comboVcoMode.currentData() or 0
                else:
                    vco_mode = self.ui.comboVcoMode.currentIndex()

                if vco_mode == 3:  # Fin0模式
                    logger.info("当前为Fin0模式，更新VCODistFreq")
                    self._update_vco_dist_freq_for_fin0_mode()
                else:
                    logger.info(f"当前VCO模式为{vco_mode}，不是Fin0模式，跳过更新")

        except Exception as e:
            logger.error(f"处理Fin0Freq变化时出错: {str(e)}")

    def _on_fin0_div2_changed(self):
        """当Div2控件状态改变时的处理函数"""
        try:
            logger.info("🔄 检测到Div2状态变化")

            # 检查当前VCO模式是否为Fin0模式
            if hasattr(self.ui, "comboVcoMode"):
                vco_mode = 0
                if hasattr(self.ui.comboVcoMode, 'currentData'):
                    vco_mode = self.ui.comboVcoMode.currentData() or 0
                else:
                    vco_mode = self.ui.comboVcoMode.currentIndex()

                if vco_mode == 3:  # Fin0模式
                    logger.info("当前为Fin0模式，更新VCODistFreq")
                    self._update_vco_dist_freq_for_fin0_mode()
                else:
                    logger.info(f"当前VCO模式为{vco_mode}，不是Fin0模式，跳过更新")

        except Exception as e:
            logger.error(f"处理Div2变化时出错: {str(e)}")

    def _on_fin0_pd_changed(self):
        """当Fin0PD控件状态改变时的处理函数"""
        try:
            logger.info("🔄 检测到Fin0PD状态变化")

            # 检查当前VCO模式是否为Fin0模式
            if hasattr(self.ui, "comboVcoMode"):
                vco_mode = 0
                if hasattr(self.ui.comboVcoMode, 'currentData'):
                    vco_mode = self.ui.comboVcoMode.currentData() or 0
                else:
                    vco_mode = self.ui.comboVcoMode.currentIndex()

                if vco_mode == 3:  # Fin0模式
                    logger.info("当前为Fin0模式，更新VCODistFreq")
                    self._update_vco_dist_freq_for_fin0_mode()
                else:
                    logger.info(f"当前VCO模式为{vco_mode}，不是Fin0模式，跳过更新")

        except Exception as e:
            logger.error(f"处理Fin0PD变化时出错: {str(e)}")

    def _update_vco_dist_freq_for_fin0_mode(self):
        """在Fin0模式下更新VCODistFreq的值

        逻辑：
        - 当comboVcoMode选择为Fin0时
        - 如果Div2被选中且Fin0PD没有被选中（不掉电）
        - 则VCODistFreq = Fin0Freq / 2
        - 否则VCODistFreq = Fin0Freq
        """
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                logger.warning("VCODistFreq控件不存在")
                return

            # 获取Fin0Freq的值
            fin0_freq = 0.0
            if hasattr(self.ui, "Fin0Freq"):
                try:
                    fin0_freq_text = self.ui.Fin0Freq.text()
                    fin0_freq = float(fin0_freq_text) if fin0_freq_text else 2949.12
                except ValueError:
                    logger.warning(f"Fin0Freq值无效: {fin0_freq_text}，使用默认值2949.12")
                    fin0_freq = 2949.12
            else:
                logger.warning("Fin0Freq控件不存在，使用默认值2949.12")
                fin0_freq = 2949.12

            # 检查Fin0PD状态（是否掉电）
            fin0_pd_checked = False
            if hasattr(self.ui, "Fin0PD"):
                fin0_pd_checked = self.ui.Fin0PD.isChecked()
            else:
                logger.warning("Fin0PD控件不存在，假设未掉电")

            # 检查Div2状态（是否选中）
            div2_checked = False
            if hasattr(self.ui, "Div2"):
                div2_checked = self.ui.Div2.isChecked()
            else:
                logger.warning("Div2控件不存在，假设未选中")

            logger.info("📊 Fin0模式计算参数:")
            logger.info(f"  Fin0Freq: {fin0_freq} MHz")
            logger.info(f"  Div2选中: {div2_checked}")
            logger.info(f"  Fin0PD选中（掉电）: {fin0_pd_checked}")

            # 计算VCODistFreq
            if fin0_pd_checked:
                # 如果Fin0掉电，VCODistFreq为0
                vco_dist_freq = 0.0
                logger.info("🔌 Fin0已掉电，VCODistFreq = 0")
            elif div2_checked:
                # 如果Div2被选中且Fin0没有掉电，VCODistFreq = Fin0Freq / 2
                vco_dist_freq = fin0_freq / 2.0
                logger.info(f"➗ Div2选中且Fin0未掉电，VCODistFreq = {fin0_freq} / 2 = {vco_dist_freq}")
            else:
                # 如果Div2未选中且Fin0没有掉电，VCODistFreq = Fin0Freq
                vco_dist_freq = fin0_freq
                logger.info(f"📍 Div2未选中且Fin0未掉电，VCODistFreq = {vco_dist_freq}")

            # 更新VCODistFreq控件的值
            self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
            logger.info(f"✅ 已更新VCODistFreq为: {vco_dist_freq:.5f} MHz")

            # 通知其他组件VCODistFreq已更新
            self._notify_vco_dist_freq_changed(vco_dist_freq)

        except Exception as e:
            logger.error(f"更新Fin0模式VCODistFreq时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _on_vco_mode_changed(self):
        """当VCO模式控件值改变时的处理函数"""
        try:
            logger.info("🔄 检测到VCO模式变化")

            # 获取当前的VCO模式值
            vco_mode = 0
            if hasattr(self.ui, "comboVcoMode"):
                if hasattr(self.ui.comboVcoMode, 'currentData'):
                    vco_mode = self.ui.comboVcoMode.currentData() or 0
                else:
                    vco_mode = self.ui.comboVcoMode.currentIndex()
                logger.info(f"🔍 当前VCO模式: {vco_mode} ({self.VCO_MODE_FREQUENCY_RANGES.get(vco_mode, {}).get('name', '未知')})")

            # 根据VCO模式更新VCODistFreq的显示值
            self._update_vco_dist_freq_for_mode(vco_mode)

            # 根据VCO模式控制Fin0PD控件状态
            self._update_fin0pd_for_vco_mode(vco_mode)

            # 根据VCO模式控制Fin0Freq控件的只读状态
            self._update_fin0freq_readonly_state(vco_mode)

            # 如果是VCO0或VCO1模式，触发正常的PLL计算
            if vco_mode in [0, 1]:
                logger.info(f"VCO模式{vco_mode}: 触发PLL频率计算")
                QtCore.QTimer.singleShot(100, self.calculate_output_frequencies)

            # 验证当前VCODistFreq值是否在范围内
            self._validate_vco_dist_freq_range()

        except Exception as e:
            logger.error(f"处理VCO模式变化时出错: {str(e)}")

    def _update_fin0pd_for_vco_mode(self, vco_mode):
        """根据VCO模式更新Fin0PD控件状态"""
        try:
            if hasattr(self.ui, "Fin0PD"):
                if vco_mode == 3:  # Fin0模式
                    # Fin0模式时，Fin0PD需要打开（不被选中）
                    self.ui.Fin0PD.setChecked(False)
                    logger.info("VCO模式为Fin0: 设置Fin0PD为打开状态（不选中）")
                else:
                    # 其他模式时，Fin0PD需要被选中
                    self.ui.Fin0PD.setChecked(True)
                    logger.info(f"VCO模式为{vco_mode}: 设置Fin0PD为选中状态")
            else:
                logger.warning("Fin0PD控件不存在，无法更新状态")
        except Exception as e:
            logger.error(f"更新Fin0PD控件状态时出错: {str(e)}")

    def _update_fin0freq_readonly_state(self, vco_mode):
        """根据VCO模式更新Fin0Freq控件的只读状态"""
        try:
            if hasattr(self.ui, "Fin0Freq"):
                if vco_mode == 3:  # Fin0模式
                    # Fin0模式时，Fin0Freq是用户输入控件，应该可编辑
                    self.ui.Fin0Freq.setReadOnly(False)
                    logger.info("VCO模式为Fin0: 设置Fin0Freq为可编辑状态")
                else:
                    # 其他模式时，Fin0Freq是计算结果显示控件，应该只读
                    self.ui.Fin0Freq.setReadOnly(True)
                    logger.info(f"VCO模式为{vco_mode}: 设置Fin0Freq为只读状态")
            else:
                logger.warning("Fin0Freq控件不存在，无法更新只读状态")
        except Exception as e:
            logger.error(f"更新Fin0Freq只读状态时出错: {str(e)}")

    def _update_vco_dist_freq_for_mode(self, vco_mode):
        """根据VCO模式更新VCODistFreq的显示值"""
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                return

            if vco_mode == 2:  # CLKin1模式
                # 显示时钟选择页面里的clkin1的值
                clkin1_freq = self.clkin_frequencies.get("ClkIn1", 0.0)
                if clkin1_freq > 0:
                    self.ui.VCODistFreq.setText(f"{clkin1_freq:.5f}")
                    logger.info(f"VCO模式CLKin1: 显示CLKin1频率 {clkin1_freq:.5f} MHz")
                else:
                    logger.warning("CLKin1频率为0，无法显示")

            elif vco_mode == 3:  # Fin0模式
                # 使用新的Fin0模式计算逻辑
                logger.info("VCO模式Fin0: 使用新的计算逻辑")
                self._update_vco_dist_freq_for_fin0_mode()
            else:
                # VCO0或VCO1模式，使用正常的PLL计算
                logger.info(f"VCO模式{vco_mode}: 使用正常PLL计算更新VCODistFreq")

        except Exception as e:
            logger.error(f"根据VCO模式更新VCODistFreq时出错: {str(e)}")

    def _validate_vco_dist_freq_range(self):
        """验证VCODistFreq值是否在当前VCO模式的频率范围内，并设置控件颜色"""
        try:
            if not hasattr(self.ui, "VCODistFreq") or not hasattr(self.ui, "comboVcoMode"):
                return

            # 获取当前VCO模式
            vco_mode = 0
            if hasattr(self.ui.comboVcoMode, 'currentData'):
                vco_mode = self.ui.comboVcoMode.currentData() or 0
            else:
                vco_mode = self.ui.comboVcoMode.currentIndex()

            # 获取当前VCODistFreq值
            vco_dist_text = self.ui.VCODistFreq.text()
            try:
                vco_dist_freq = float(vco_dist_text) if vco_dist_text else 0.0
            except ValueError:
                # 如果值无效，设置为红色
                self._set_vco_dist_freq_style(False)
                return

            # 防重复验证：检查是否与上次验证的值相同
            current_validation_key = f"{vco_mode}_{vco_dist_freq:.6f}"
            if hasattr(self, '_last_validation_key') and self._last_validation_key == current_validation_key:
                return  # 跳过重复验证

            self._last_validation_key = current_validation_key

            # 获取模式配置
            mode_config = self.VCO_MODE_FREQUENCY_RANGES.get(vco_mode, {})

            # 对于VCO0和VCO1模式，检查频率范围
            if vco_mode in [0, 1]:
                min_freq = mode_config.get("min", 0)
                max_freq = mode_config.get("max", float('inf'))

                # 检查频率是否在范围内
                is_in_range = min_freq <= vco_dist_freq <= max_freq

                logger.info(f"VCO模式{vco_mode}频率验证: {vco_dist_freq:.3f} MHz, 范围: {min_freq}-{max_freq} MHz, 结果: {'✓' if is_in_range else '✗'}")

                # 设置控件样式
                self._set_vco_dist_freq_style(is_in_range)

            else:
                # 对于CLKin1和Fin0模式，不进行范围验证，使用正常样式
                self._set_vco_dist_freq_style(True)

        except Exception as e:
            logger.error(f"验证VCODistFreq频率范围时出错: {str(e)}")

    def _set_vco_dist_freq_style(self, is_valid):
        """设置VCODistFreq控件的样式"""
        try:
            if not hasattr(self.ui, "VCODistFreq"):
                return

            if is_valid:
                # 正常样式（清除红色背景）
                self.ui.VCODistFreq.setStyleSheet("")
            else:
                # 红色背景样式
                self.ui.VCODistFreq.setStyleSheet("QLineEdit { background-color: #ffcccc; }")

        except Exception as e:
            logger.error(f"设置VCODistFreq样式时出错: {str(e)}")

    def closeEvent(self, event):
        """重写closeEvent方法，在窗口关闭时缓存所有LineEdit值"""
        try:
            # 缓存所有LineEdit控件的当前值
            self._cache_all_lineedit_values()
            logger.info("PLL窗口关闭，已缓存所有LineEdit控件的值")
        except Exception as e:
            logger.error(f"窗口关闭时缓存LineEdit值失败: {str(e)}")

        # 调用父类的closeEvent方法
        super().closeEvent(event)


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernPLLHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
